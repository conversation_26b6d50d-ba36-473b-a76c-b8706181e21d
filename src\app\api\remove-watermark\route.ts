import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType, CreditsAmount, getUserCredits } from "@/services/credit";
import { getWatermarkConfig, validateWatermarkConfig, getConfigErrorMessage } from "@/services/watermark";

// 移除 edge runtime，因为需要使用 Buffer

export async function POST(req: NextRequest) {
  try {
    // 获取并验证去水印API配置
    const config = getWatermarkConfig();
    console.log("=== Watermark Config Debug ===");
    console.log("API URL:", config.apiUrl);
    console.log("App ID:", config.appId);
    console.log("Secret Code (first 10 chars):", config.secretCode.substring(0, 10) + "...");
    console.log("Config valid:", validateWatermarkConfig(config));

    if (!validateWatermarkConfig(config)) {
      const errorMessage = getConfigErrorMessage(config);
      console.error("Watermark API configuration is invalid:", errorMessage);
      return NextResponse.json({ error: "去水印服务配置错误，请联系管理员" }, { status: 500 });
    }

    // 检查用户认证
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return NextResponse.json({ error: "用户未登录" }, { status: 401 });
    }

    // 检查用户积分
    const userCredits = await getUserCredits(user_uuid);
    if (!userCredits || userCredits.left_credits < CreditsAmount.RemoveWatermarkCost) {
      return NextResponse.json({
        error: "积分不足，请先充值",
        required_credits: CreditsAmount.RemoveWatermarkCost,
        current_credits: userCredits?.left_credits || 0
      }, { status: 402 });
    }

    const formData = await req.formData();
    const file = formData.get("file");
    if (!file || !(file instanceof Blob)) {
      return NextResponse.json({ error: "未上传图片文件" }, { status: 400 });
    }

    // 将图片转为二进制流
    const arrayBuffer = await file.arrayBuffer();

    const res = await fetch(config.apiUrl, {
      method: "POST",
      headers: {
        "x-ti-app-id": config.appId,
        "x-ti-secret-code": config.secretCode,
        "Content-Type": "application/octet-stream",
      },
      body: Buffer.from(arrayBuffer),
    });

    console.log("API Response Status:", res.status);
    const data = await res.json();
    console.log("API Response Data:", JSON.stringify(data, null, 2));

    if (data && data.code === 200 && data.result && data.result.image) {
      // API调用成功，扣减积分
      try {
        await decreaseCredits({
          user_uuid,
          trans_type: CreditsTransType.RemoveWatermark,
          credits: CreditsAmount.RemoveWatermarkCost,
        });
        console.log("Successfully decreased credits for user:", user_uuid);
      } catch (creditError) {
        console.error("Failed to decrease credits:", creditError);
        return NextResponse.json({ error: "积分扣除失败" }, { status: 500 });
      }

      // 返回base64图片
      return NextResponse.json({ image: data.result.image });
    } else {
      console.log("API call failed. Response code:", data.code);
      console.log("Error message:", data.message || data.msg);

      // 根据不同的错误码返回不同的状态码和消息
      if (data.code === 40003) {
        // TextIn API余额不足
        return NextResponse.json({
          error: "去水印服务商账户余额不足，请联系管理员充值API账户",
          detail: data
        }, { status: 503 }); // 503 Service Unavailable
      } else if (data.code === 40001) {
        // 认证失败
        return NextResponse.json({
          error: "去水印API认证失败，请检查配置",
          detail: data
        }, { status: 500 });
      } else {
        // 其他错误
        return NextResponse.json({
          error: data.message || data.msg || "去水印失败",
          detail: data
        }, { status: 500 });
      }
    }
  } catch (error) {
    console.error("remove watermark failed:", error);
    return NextResponse.json({
      error: "服务器错误",
      detail: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}