import { systemLogs } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, count, gte, lte } from "drizzle-orm";

export enum LogLevel {
  Info = "info",
  Warn = "warn",
  Error = "error",
  Debug = "debug",
}

export enum LogModule {
  User = "user",
  Order = "order",
  Feedback = "feedback",
  Contact = "contact",
  Admin = "admin",
  Auth = "auth",
  Payment = "payment",
  System = "system",
}

export interface LogEntry {
  level: LogLevel;
  action: string;
  message: string;
  details?: any;
  user_uuid?: string;
  user_email?: string;
  ip_address?: string;
  user_agent?: string;
  module: LogModule;
  resource_id?: string;
}

export async function insertLog(
  data: LogEntry
): Promise<typeof systemLogs.$inferSelect | undefined> {
  try {
    const logData = {
      ...data,
      details: data.details ? JSON.stringify(data.details) : null,
      created_at: new Date(),
    };

    const [log] = await db().insert(systemLogs).values(logData).returning();
    return log;
  } catch (error) {
    console.error('Failed to insert log:', error);
    return undefined;
  }
}

export async function getLogs(
  page: number = 1,
  limit: number = 50,
  filters?: {
    level?: string;
    module?: string;
    user_uuid?: string;
    startDate?: Date;
    endDate?: Date;
  }
): Promise<(typeof systemLogs.$inferSelect)[] | undefined> {
  try {
    const offset = (page - 1) * limit;

    const baseQuery = db()
      .select()
      .from(systemLogs)
      .orderBy(desc(systemLogs.created_at))
      .limit(limit)
      .offset(offset);

    // 应用过滤条件
    const conditions = [];
    if (filters?.level) {
      conditions.push(eq(systemLogs.level, filters.level));
    }
    if (filters?.module) {
      conditions.push(eq(systemLogs.module, filters.module));
    }
    if (filters?.user_uuid) {
      conditions.push(eq(systemLogs.user_uuid, filters.user_uuid));
    }
    if (filters?.startDate) {
      conditions.push(gte(systemLogs.created_at, filters.startDate));
    }
    if (filters?.endDate) {
      conditions.push(lte(systemLogs.created_at, filters.endDate));
    }

    const data = conditions.length > 0
      ? await baseQuery.where(and(...conditions))
      : await baseQuery;

    return data || [];
  } catch (error) {
    console.error('Failed to get logs:', error);
    return [];
  }
}

export async function getLogsTotal(
  filters?: {
    level?: string;
    module?: string;
    user_uuid?: string;
    startDate?: Date;
    endDate?: Date;
  }
): Promise<number> {
  try {
    const baseQuery = db().select({ count: count() }).from(systemLogs);

    // 应用过滤条件
    const conditions = [];
    if (filters?.level) {
      conditions.push(eq(systemLogs.level, filters.level));
    }
    if (filters?.module) {
      conditions.push(eq(systemLogs.module, filters.module));
    }
    if (filters?.user_uuid) {
      conditions.push(eq(systemLogs.user_uuid, filters.user_uuid));
    }
    if (filters?.startDate) {
      conditions.push(gte(systemLogs.created_at, filters.startDate));
    }
    if (filters?.endDate) {
      conditions.push(lte(systemLogs.created_at, filters.endDate));
    }

    const result = conditions.length > 0
      ? await baseQuery.where(and(...conditions))
      : await baseQuery;

    return result[0]?.count || 0;
  } catch (error) {
    console.error('Failed to get logs total:', error);
    return 0;
  }
}

export async function getLogStats(): Promise<{
  total: number;
  byLevel: { [key: string]: number };
  byModule: { [key: string]: number };
  recentErrors: number;
}> {
  try {
    const allLogs = await db().select().from(systemLogs);
    
    const total = allLogs.length;
    const byLevel: { [key: string]: number } = {};
    const byModule: { [key: string]: number } = {};
    
    // 计算最近24小时的错误数量
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const recentErrors = allLogs.filter(log => 
      log.level === LogLevel.Error && 
      log.created_at && 
      new Date(log.created_at) > yesterday
    ).length;

    allLogs.forEach(log => {
      // 统计日志级别
      if (log.level) {
        byLevel[log.level] = (byLevel[log.level] || 0) + 1;
      }

      // 统计模块
      if (log.module) {
        byModule[log.module] = (byModule[log.module] || 0) + 1;
      }
    });

    return {
      total,
      byLevel,
      byModule,
      recentErrors,
    };
  } catch (error) {
    console.error('Failed to get log stats:', error);
    return {
      total: 0,
      byLevel: {},
      byModule: {},
      recentErrors: 0,
    };
  }
}

export async function deleteOldLogs(daysToKeep: number = 30): Promise<number> {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    // 先查询要删除的记录数量
    const countResult = await db()
      .select({ count: count() })
      .from(systemLogs)
      .where(lte(systemLogs.created_at, cutoffDate));

    const deleteCount = countResult[0]?.count || 0;

    if (deleteCount > 0) {
      await db()
        .delete(systemLogs)
        .where(lte(systemLogs.created_at, cutoffDate));
    }

    return deleteCount;
  } catch (error) {
    console.error('Failed to delete old logs:', error);
    return 0;
  }
}

// 便捷的日志记录函数
export async function logInfo(action: string, message: string, options?: Partial<LogEntry>) {
  return insertLog({
    level: LogLevel.Info,
    action,
    message,
    module: LogModule.System,
    ...options,
  });
}

export async function logWarn(action: string, message: string, options?: Partial<LogEntry>) {
  return insertLog({
    level: LogLevel.Warn,
    action,
    message,
    module: LogModule.System,
    ...options,
  });
}

export async function logError(action: string, message: string, options?: Partial<LogEntry>) {
  return insertLog({
    level: LogLevel.Error,
    action,
    message,
    module: LogModule.System,
    ...options,
  });
}

export async function logDebug(action: string, message: string, options?: Partial<LogEntry>) {
  return insertLog({
    level: LogLevel.Debug,
    action,
    message,
    module: LogModule.System,
    ...options,
  });
}
