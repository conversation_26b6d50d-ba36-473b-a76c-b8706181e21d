import { PostStatus, findPostBySlug, insertPost } from "@/models/post";
import Empty from "@/components/blocks/empty";
import PostForm from "@/components/admin/post-form";
import Header from "@/components/dashboard/header";
import { getUserInfo } from "@/services/user";
import { getUuid } from "@/lib/hash";

export default async function () {
  const user = await getUserInfo();
  if (!user || !user.uuid) {
    return <Empty message="no auth" />;
  }

  const handleSubmit = async (formData: any) => {
    "use server";

    const { title, slug, locale, status, description, cover_url, author_name, author_avatar_url, content } = formData;

    if (!title?.trim() || !slug?.trim() || !locale?.trim()) {
      throw new Error("请填写必填字段");
    }

    const existPost = await findPostBySlug(slug, locale);
    if (existPost) {
      throw new Error("相同链接的文章已存在");
    }

    const post = {
      uuid: getUuid(),
      created_at: new Date(),
      status: status as PostStatus,
      title,
      slug,
      locale,
      description,
      cover_url,
      author_name,
      author_avatar_url,
      content,
    };

    try {
      await insertPost(post);
      return {
        status: "success",
        message: "文章创建成功",
      };
    } catch (err: any) {
      throw new Error(err.message);
    }
  };

  return (
    <>
      <Header crumb={{
        items: [
          { title: "文章管理", url: "/admin/posts" },
          { title: "添加文章", is_active: true },
        ]
      }} />

      <div className="w-full px-4 md:px-8 py-8">
        <h1 className="text-2xl font-medium mb-8">添加文章</h1>
        <PostForm onSubmit={handleSubmit} />
      </div>
    </>
  );
}
