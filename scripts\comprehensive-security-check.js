#!/usr/bin/env node

/**
 * 全面安全诊断脚本
 * 检查HTTPS、证书、混合内容、浏览器安全策略等问题
 */

const https = require('https');
const http = require('http');
const tls = require('tls');
const url = require('url');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🔍 开始全面安全诊断...');
console.log(`检查网站: ${SITE_URL}`);

// 1. 检查HTTPS协议和证书
function checkHTTPSAndCertificate(siteUrl) {
  return new Promise((resolve, reject) => {
    console.log('\n🔐 HTTPS协议和证书检查:');
    
    if (!siteUrl.startsWith('https://')) {
      console.log('❌ 网站未使用HTTPS协议');
      resolve();
      return;
    }
    
    console.log('✅ 网站使用HTTPS协议');
    
    const parsedUrl = url.parse(siteUrl);
    const options = {
      hostname: parsedUrl.hostname,
      port: 443,
      method: 'GET',
      rejectUnauthorized: false // 我们手动检查证书
    };
    
    const req = https.request(options, (res) => {
      const cert = res.socket.getPeerCertificate();
      
      if (cert && Object.keys(cert).length > 0) {
        console.log('✅ SSL证书存在');
        
        // 检查证书有效期
        const now = new Date();
        const validFrom = new Date(cert.valid_from);
        const validTo = new Date(cert.valid_to);
        
        console.log(`📅 证书有效期: ${cert.valid_from} 到 ${cert.valid_to}`);
        
        if (now < validFrom) {
          console.log('❌ 证书尚未生效');
        } else if (now > validTo) {
          console.log('❌ 证书已过期');
        } else {
          const daysLeft = Math.ceil((validTo - now) / (1000 * 60 * 60 * 24));
          console.log(`✅ 证书有效 (还有 ${daysLeft} 天过期)`);
        }
        
        // 检查证书颁发者
        console.log(`🏢 证书颁发者: ${cert.issuer.CN || cert.issuer.O}`);
        
        // 检查证书主题
        console.log(`📋 证书主题: ${cert.subject.CN}`);
        
        // 检查是否为自签名证书
        if (cert.issuer.CN === cert.subject.CN) {
          console.log('⚠️ 检测到自签名证书');
        } else {
          console.log('✅ 证书由受信任的CA颁发');
        }
        
      } else {
        console.log('❌ 无法获取SSL证书信息');
      }
      
      resolve();
    });
    
    req.on('error', (err) => {
      console.log(`❌ HTTPS连接失败: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ HTTPS连接超时');
      resolve();
    });
    
    req.end();
  });
}

// 2. 检查HTTP重定向
function checkHTTPRedirect(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🔄 HTTP重定向检查:');
    
    const httpUrl = siteUrl.replace('https://', 'http://');
    const parsedUrl = url.parse(httpUrl);
    
    const req = http.request({
      hostname: parsedUrl.hostname,
      port: 80,
      path: parsedUrl.path,
      method: 'GET'
    }, (res) => {
      if (res.statusCode >= 300 && res.statusCode < 400) {
        const location = res.headers.location;
        if (location && location.startsWith('https://')) {
          console.log('✅ HTTP自动重定向到HTTPS');
          console.log(`   重定向到: ${location}`);
        } else {
          console.log('⚠️ HTTP重定向但不是到HTTPS');
          console.log(`   重定向到: ${location}`);
        }
      } else {
        console.log('❌ HTTP没有重定向到HTTPS');
        console.log(`   HTTP状态码: ${res.statusCode}`);
      }
      resolve();
    });
    
    req.on('error', (err) => {
      console.log(`⚠️ 无法测试HTTP重定向: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('⚠️ HTTP重定向测试超时');
      resolve();
    });
    
    req.end();
  });
}

// 3. 检查混合内容
function checkMixedContent(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🔗 混合内容检查:');
    
    const req = https.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        // 检查HTTP资源引用
        const httpResources = [];
        
        // 检查图片
        const imgMatches = html.match(/src\s*=\s*["']http:\/\/[^"']+/gi);
        if (imgMatches) {
          httpResources.push(...imgMatches.map(m => m.replace(/src\s*=\s*["']/, '')));
        }
        
        // 检查脚本
        const scriptMatches = html.match(/<script[^>]+src\s*=\s*["']http:\/\/[^"']+/gi);
        if (scriptMatches) {
          httpResources.push(...scriptMatches.map(m => m.match(/http:\/\/[^"']+/)[0]));
        }
        
        // 检查样式表
        const linkMatches = html.match(/<link[^>]+href\s*=\s*["']http:\/\/[^"']+/gi);
        if (linkMatches) {
          httpResources.push(...linkMatches.map(m => m.match(/http:\/\/[^"']+/)[0]));
        }
        
        if (httpResources.length > 0) {
          console.log('❌ 发现混合内容问题:');
          httpResources.forEach(resource => {
            console.log(`   - ${resource}`);
          });
        } else {
          console.log('✅ 未发现混合内容问题');
        }
        
        resolve();
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 无法检查混合内容: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 混合内容检查超时');
      resolve();
    });
  });
}

// 4. 检查安全头
function checkSecurityHeaders(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🛡️ 安全头检查:');
    
    const req = https.get(siteUrl, (res) => {
      const securityHeaders = {
        'strict-transport-security': 'HSTS (强制HTTPS)',
        'content-security-policy': 'CSP (内容安全策略)',
        'x-frame-options': 'X-Frame-Options (防点击劫持)',
        'x-content-type-options': 'X-Content-Type-Options (防MIME嗅探)',
        'referrer-policy': 'Referrer-Policy (引用策略)',
        'permissions-policy': 'Permissions-Policy (权限策略)'
      };
      
      Object.entries(securityHeaders).forEach(([header, description]) => {
        if (res.headers[header]) {
          console.log(`✅ ${description}: ${res.headers[header]}`);
        } else {
          console.log(`❌ ${description}: 未设置`);
        }
      });
      
      resolve();
    });
    
    req.on('error', (err) => {
      console.log(`❌ 无法检查安全头: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 安全头检查超时');
      resolve();
    });
  });
}

// 主函数
async function main() {
  try {
    await checkHTTPSAndCertificate(SITE_URL);
    await checkHTTPRedirect(SITE_URL);
    await checkMixedContent(SITE_URL);
    await checkSecurityHeaders(SITE_URL);
    
    console.log('\n✅ 全面安全诊断完成!');
    console.log('\n📋 诊断总结:');
    console.log('1. HTTPS协议和证书状态');
    console.log('2. HTTP到HTTPS重定向配置');
    console.log('3. 混合内容检查结果');
    console.log('4. 安全头配置状态');
    
    console.log('\n💡 如果发现问题:');
    console.log('- 证书问题: 联系域名/主机提供商');
    console.log('- 混合内容: 将HTTP资源改为HTTPS');
    console.log('- 安全头: 检查服务器配置');
    console.log('- 重定向: 配置服务器自动重定向');
    
  } catch (error) {
    console.error('❌ 安全诊断失败:', error.message);
    process.exit(1);
  }
}

main();
