import { respData, respErr } from "@/lib/resp";
import { findPostByUuid, updatePost } from "@/models/post";
import { isCurrentUserAdmin } from "@/services/admin";

export async function GET(req: Request, { params }: { params: Promise<{ uuid: string }> }) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { uuid } = await params;
    if (!uuid) {
      return respErr("无效的UUID");
    }

    const post = await findPostByUuid(uuid);
    if (!post) {
      return respErr("文章不存在");
    }

    return respData(post);
  } catch (e) {
    console.log("get post failed: ", e);
    return respErr("获取文章失败");
  }
}

export async function DELETE(req: Request, { params }: { params: Promise<{ uuid: string }> }) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { uuid } = await params;
    if (!uuid) {
      return respErr("无效的UUID");
    }

    const post = await findPostByUuid(uuid);
    if (!post) {
      return respErr("文章不存在");
    }

    // 软删除：将状态设置为deleted
    const success = await updatePost(uuid, { 
      status: "deleted",
      updated_at: new Date()
    });
    
    if (!success) {
      return respErr("删除失败");
    }

    return respData({ message: "删除成功" });
  } catch (e) {
    console.log("delete post failed: ", e);
    return respErr("删除文章失败");
  }
}
