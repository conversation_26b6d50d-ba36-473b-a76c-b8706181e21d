'use client'

import Script from 'next/script'

export default function OrganizationSchema() {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top'
  
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Watermark Remover",
    "description": "Professional AI-powered watermark removal tool",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "foundingDate": "2024",
    "sameAs": [
      // Add social media URLs here when available
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["English", "Chinese", "French", "Portuguese", "Russian"]
    },
    "offers": {
      "@type": "AggregateOffer",
      "priceCurrency": "USD",
      "lowPrice": "0",
      "highPrice": "71.43",
      "offerCount": "6"
    },
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser"
  }

  return (
    <Script
      id="organization-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(organizationSchema)
      }}
    />
  )
}
