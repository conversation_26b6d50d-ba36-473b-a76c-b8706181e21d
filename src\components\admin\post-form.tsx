"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ImageUpload from "@/components/ui/image-upload";
import MarkdownEditor from "@/components/blocks/mdeditor";
import { generateSlug, generateSlugForChinese } from "@/lib/slug";
import { toast } from "sonner";
import { useRouter } from "@/i18n/navigation";
import { PostStatus } from "@/models/post";
import { localeNames, locales } from "@/i18n/locale";

interface PostFormProps {
  initialData?: any;
  isEdit?: boolean;
  onSubmit: (data: any) => Promise<any>;
}

export default function PostForm({ initialData, isEdit = false, onSubmit }: PostFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    slug: initialData?.slug || '',
    locale: initialData?.locale || 'en',
    status: initialData?.status || PostStatus.Created,
    description: initialData?.description || '',
    cover_url: initialData?.cover_url || '',
    author_name: initialData?.author_name || '',
    author_avatar_url: initialData?.author_avatar_url || '',
    content: initialData?.content || '',
  });

  // 自动生成slug
  useEffect(() => {
    if (!isEdit && formData.title && !formData.slug) {
      const autoSlug = formData.locale === 'zh' 
        ? generateSlugForChinese(formData.title)
        : generateSlug(formData.title);
      setFormData(prev => ({ ...prev, slug: autoSlug }));
    }
  }, [formData.title, formData.locale, isEdit]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.slug.trim() || !formData.locale.trim()) {
      toast.error('请填写必填字段');
      return;
    }

    setLoading(true);
    try {
      const result = await onSubmit(formData);
      if (result.status === 'success') {
        toast.success(result.message);
        router.push('/admin/posts');
      } else {
        toast.error(result.message || '操作失败');
      }
    } catch (error: any) {
      toast.error(error.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-2xl">
      <div className="space-y-2">
        <Label htmlFor="title">标题 *</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          placeholder="文章标题"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="slug">链接 *</Label>
        <Input
          id="slug"
          value={formData.slug}
          onChange={(e) => handleInputChange('slug', e.target.value)}
          placeholder="what-is-shipany"
          required
        />
        <p className="text-sm text-muted-foreground">
          文章链接应该是唯一的，访问地址如：/blog/{formData.slug}
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="locale">语言 *</Label>
          <Select value={formData.locale} onValueChange={(value) => handleInputChange('locale', value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择语言" />
            </SelectTrigger>
            <SelectContent>
              {locales.map((locale) => (
                <SelectItem key={locale} value={locale}>
                  {localeNames[locale]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">状态</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              {Object.values(PostStatus).map((status) => (
                <SelectItem key={status} value={status}>
                  {status}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="文章描述"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label>封面图片</Label>
        <ImageUpload
          value={formData.cover_url}
          onChange={(url) => handleInputChange('cover_url', url)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="author_name">作者姓名</Label>
        <Input
          id="author_name"
          value={formData.author_name}
          onChange={(e) => handleInputChange('author_name', e.target.value)}
          placeholder="作者姓名"
        />
      </div>

      <div className="space-y-2">
        <Label>作者头像</Label>
        <ImageUpload
          value={formData.author_avatar_url}
          onChange={(url) => handleInputChange('author_avatar_url', url)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="content">内容</Label>
        <MarkdownEditor
          value={formData.content}
          onChange={(value) => handleInputChange('content', value)}
        />
      </div>

      <div className="flex gap-4">
        <Button type="submit" disabled={loading}>
          {loading ? '提交中...' : (isEdit ? '更新文章' : '创建文章')}
        </Button>
        <Button type="button" variant="outline" onClick={() => router.back()}>
          取消
        </Button>
      </div>
    </form>
  );
}
