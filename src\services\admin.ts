import { findUserByUuid, updateUserInfo, findUserByEmail } from "@/models/user";
import { getUserUuid } from "./user";
import { getAllAdminEmails } from "@/config/admin";

// 检查当前用户是否为管理员
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return false;
    }

    const user = await findUserByUuid(userUuid);
    if (!user || !user.email) {
      return false;
    }

    // 检查用户是否已经标记为管理员
    if (user.is_admin) {
      return true;
    }

    // 检查邮箱是否在管理员列表中
    const adminEmails = getAllAdminEmails();
    const isAdmin = adminEmails.includes(user.email.toLowerCase());

    // 如果邮箱在管理员列表中但数据库中未标记，则更新数据库
    if (isAdmin && !user.is_admin) {
      try {
        await updateUserInfo(userUuid, { is_admin: true });
      } catch (error) {
        console.log('Failed to update admin status in database:', error);
      }
    }

    return isAdmin;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// 检查指定用户是否为管理员
export async function isUserAdmin(userUuid: string): Promise<boolean> {
  try {
    const user = await findUserByUuid(userUuid);
    if (!user || !user.email) {
      return false;
    }

    // 检查用户是否已经标记为管理员
    if (user.is_admin) {
      return true;
    }

    // 检查邮箱是否在管理员列表中
    const adminEmails = getAllAdminEmails();
    return adminEmails.includes(user.email.toLowerCase());
  } catch (error) {
    console.error('Error checking user admin status:', error);
    return false;
  }
}

// 获取当前用户的管理员信息
export async function getCurrentUserAdminInfo() {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return null;
    }

    const user = await findUserByUuid(userUuid);
    if (!user) {
      return null;
    }

    const isAdmin = await isCurrentUserAdmin();
    
    return {
      user,
      isAdmin,
    };
  } catch (error) {
    console.error('Error getting admin info:', error);
    return null;
  }
}
