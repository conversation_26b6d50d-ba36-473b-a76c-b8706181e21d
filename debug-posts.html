<!DOCTYPE html>
<html>
<head>
    <title>调试Posts数据</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .result { 
            margin: 20px 0; 
            padding: 10px; 
            background: #f5f5f5; 
            border-radius: 5px;
        }
        pre { 
            white-space: pre-wrap; 
            word-wrap: break-word; 
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Posts数据调试</h1>
    
    <button onclick="checkDebugAPI()">检查调试API</button>
    <button onclick="checkAdminAPI()">检查管理API</button>
    <button onclick="checkFrontendAPI()">检查前台数据</button>
    <button onclick="createTestPost()">创建测试文章</button>
    <button onclick="checkDatabase()">检查数据库</button>
    
    <div id="result" class="result" style="display:none;">
        <h3>结果：</h3>
        <pre id="resultText"></pre>
    </div>

    <script>
        async function checkDebugAPI() {
            try {
                const response = await fetch('/api/debug/posts');
                const result = await response.json();
                
                document.getElementById('resultText').textContent = 
                    '调试API结果:\n' + JSON.stringify(result, null, 2);
                document.getElementById('result').style.display = 'block';
            } catch (error) {
                document.getElementById('resultText').textContent = '调试API错误: ' + error.message;
                document.getElementById('result').style.display = 'block';
            }
        }

        async function checkAdminAPI() {
            try {
                const response = await fetch('/api/admin/posts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ page: 1, limit: 20 })
                });
                const result = await response.json();
                
                document.getElementById('resultText').textContent = 
                    '管理API结果:\n' + JSON.stringify(result, null, 2);
                document.getElementById('result').style.display = 'block';
            } catch (error) {
                document.getElementById('resultText').textContent = '管理API错误: ' + error.message;
                document.getElementById('result').style.display = 'block';
            }
        }

        async function checkFrontendAPI() {
            document.getElementById('resultText').textContent =
                '前台页面调试信息:\n' +
                '访问 /zh/posts 页面，查看浏览器控制台的 "Posts page debug" 信息\n' +
                '或者查看开发服务器终端输出';
            document.getElementById('result').style.display = 'block';
        }

        async function createTestPost() {
            try {
                const response = await fetch('/api/debug/create-test-post', {
                    method: 'POST'
                });
                const result = await response.json();

                document.getElementById('resultText').textContent =
                    '创建测试文章结果:\n' + JSON.stringify(result, null, 2);
                document.getElementById('result').style.display = 'block';
            } catch (error) {
                document.getElementById('resultText').textContent = '创建测试文章错误: ' + error.message;
                document.getElementById('result').style.display = 'block';
            }
        }

        async function checkDatabase() {
            try {
                const response = await fetch('/api/debug/db');
                const result = await response.json();

                document.getElementById('resultText').textContent =
                    '数据库查询结果:\n' + JSON.stringify(result, null, 2);
                document.getElementById('result').style.display = 'block';
            } catch (error) {
                document.getElementById('resultText').textContent = '数据库查询错误: ' + error.message;
                document.getElementById('result').style.display = 'block';
            }
        }
    </script>
</body>
</html>
