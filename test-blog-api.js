// 简单的API测试脚本
const baseUrl = 'http://localhost:3001';

async function testBlogAPI() {
  console.log('开始测试Blog API...');

  try {
    // 测试获取posts列表
    console.log('\n1. 测试获取posts列表...');
    const postsResponse = await fetch(`${baseUrl}/api/admin/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        page: 1,
        limit: 20,
      }),
    });

    const postsResult = await postsResponse.json();
    console.log('Posts API响应:', postsResult);

    // 测试图片上传API
    console.log('\n2. 测试图片上传API结构...');
    console.log('图片上传API地址:', `${baseUrl}/api/upload`);
    console.log('需要POST请求，FormData格式，包含file字段');

    // 测试slug生成
    console.log('\n3. 测试slug生成功能...');
    const { generateSlug, generateSlugForChinese } = require('./src/lib/slug.ts');
    
    const testTitles = [
      'How to Remove Watermarks',
      '如何去除水印',
      'Best Watermark Removal Tools 2024',
      '最佳水印去除工具推荐',
    ];

    testTitles.forEach(title => {
      const slug = title.includes('如何') || title.includes('最佳') 
        ? generateSlugForChinese(title)
        : generateSlug(title);
      console.log(`标题: "${title}" -> slug: "${slug}"`);
    });

    console.log('\n✅ Blog API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testBlogAPI();
}

module.exports = { testBlogAPI };
