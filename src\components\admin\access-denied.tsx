'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Shield, Home, ArrowLeft, Mail } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function AccessDenied() {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 dark:from-slate-900 dark:via-red-900/20 dark:to-slate-900">
      <div className="max-w-md mx-auto text-center px-6">
        <div className="mb-8">
          {/* 图标 */}
          <div className="flex justify-center mb-6">
            <div className="relative">
              <Shield className="w-24 h-24 text-red-400 dark:text-red-500" />
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xl font-bold">!</span>
              </div>
            </div>
          </div>
          
          {/* 标题 */}
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            访问被拒绝
          </h1>
          
          {/* 描述 */}
          <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
            抱歉，您没有权限访问管理后台。只有系统管理员才能访问此区域。
          </p>
          
          {/* 详细说明 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6 border border-red-200 dark:border-red-800">
            <div className="flex items-start gap-3">
              <Mail className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                  需要管理员权限
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-xs">
                  如果您认为这是一个错误，请联系系统管理员将您的邮箱添加到管理员列表中。
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="space-y-3">
          <Button 
            onClick={() => router.back()} 
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回上一页
          </Button>
          
          <Button variant="outline" asChild className="w-full">
            <Link href="/">
              <Home className="w-4 h-4 mr-2" />
              回到首页
            </Link>
          </Button>
        </div>
        
        {/* 帮助信息 */}
        <div className="mt-8 text-xs text-gray-500 dark:text-gray-400">
          <p>需要帮助？请联系技术支持</p>
        </div>
      </div>
    </div>
  )
}
