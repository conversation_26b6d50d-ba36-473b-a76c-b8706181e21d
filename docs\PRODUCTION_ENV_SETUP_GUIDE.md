# 🚀 生产环境变量配置指南

## 📋 **必须修改的环境变量清单**

部署后，您需要在生产环境（Vercel/Netlify等）中设置以下环境变量：

## 🔐 **1. 认证相关 (必须修改)**

### **NextAuth配置**:
```bash
# 生产环境认证密钥 (必须重新生成)
AUTH_SECRET="kV6sMK3o8OeUzxXVr+/+K+qR2oHy0LyXnS0+S1exLoo="

# 生产环境认证URL (必须修改)
AUTH_URL="https://watermarkremover.top/api/auth"
NEXTAUTH_URL="https://watermarkremover.top"

# 信任主机设置
AUTH_TRUST_HOST=true
```

### **Google OAuth配置**:
```bash
# Google OAuth (检查域名配置)
AUTH_GOOGLE_ID="1057901697944-j7jincho98hkmmo8ltv4ci5vgl0ob6bt.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET="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"
NEXT_PUBLIC_AUTH_GOOGLE_ID="1057901697944-j7jincho98hkmmo8ltv4ci5vgl0ob6bt.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="true"

# 安全合规设置 (保持禁用)
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED="false"
```

## 🌐 **2. 网站URL配置 (必须修改)**

```bash
# 生产环境网站URL
NEXT_PUBLIC_WEB_URL="https://watermarkremover.top"
NEXT_PUBLIC_PROJECT_NAME="Watermark Remover"
```

## 💳 **3. 支付配置 (必须修改)**

### **PayPal配置**:
```bash
# 支付提供商
PAYMENT_PROVIDER="paypal"

# PayPal生产环境配置 (必须修改)
PAYPAL_CLIENT_ID="AVvv9oWdOrBP5N5kd-Pw2lDDTvZjzTZyhLt4es9eiyD2V-RL3xMW-Q1JuS_EJjR3wwsVL3sEvdgDr3Nj"
PAYPAL_CLIENT_SECRET="EJfkUMpwiR3uG2G2uQFJDG5LEW8LEsP5sdbsXmdgsqFDhRQQfXkinyWgQEBL-z5PxXmgp37eQTbSX2AM"
PAYPAL_ENVIRONMENT="live"  # 改为 live

# 支付回调URL (必须修改)
NEXT_PUBLIC_PAY_SUCCESS_URL="https://watermarkremover.top/payment-success"
NEXT_PUBLIC_PAY_FAIL_URL="https://watermarkremover.top/pricing?error=payment_failed"
NEXT_PUBLIC_PAY_CANCEL_URL="https://watermarkremover.top/pricing?cancelled=true"
```

## 🗄️ **4. 数据库配置 (检查)**

```bash
# Supabase数据库 (检查连接)
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

## 🔧 **5. API服务配置 (检查)**

```bash
# 去水印API配置
WATERMARK_API_URL="https://api.textin.com/ai/service/v1/image/watermark_remove"
WATERMARK_APP_ID="d116412bff6bb3272cddfcd730bc99a8"
WATERMARK_SECRET_CODE="c1eddc385f74f8cc9a80ec3614a446c5"
```

## 👤 **6. 管理员配置**

```bash
# 管理员邮箱
ADMIN_EMAILS="<EMAIL>"
```

## 📊 **7. 可选配置**

```bash
# 主题设置
NEXT_PUBLIC_DEFAULT_THEME="light"

# 语言检测
NEXT_PUBLIC_LOCALE_DETECTION="true"

# Google Analytics (可选)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# Google AdSense (可选)
NEXT_PUBLIC_GOOGLE_ADCODE=""
```

---

## 🚨 **关键修改步骤**

### **步骤1: Google OAuth域名配置** ⚠️ **重要**

您需要在Google Cloud Console中添加生产域名：

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择您的项目
3. 进入 "APIs & Services" > "Credentials"
4. 编辑OAuth 2.0客户端ID
5. 在"已授权的重定向URI"中添加：
   ```
   https://watermarkremover.top/api/auth/callback/google
   ```
6. 在"已授权的JavaScript来源"中添加：
   ```
   https://watermarkremover.top
   ```

### **步骤2: PayPal生产环境配置** ⚠️ **重要**

1. 登录 [PayPal Developer](https://developer.paypal.com/)
2. 切换到"Live"环境
3. 创建新的Live应用
4. 获取Live环境的Client ID和Secret
5. 更新环境变量中的PayPal配置

### **步骤3: 生成新的AUTH_SECRET** ⚠️ **安全**

```bash
# 生成新的认证密钥
openssl rand -base64 32
```

## 🔧 **Vercel部署配置**

如果使用Vercel部署，在项目设置中添加以下环境变量：

```bash
# 必须设置的变量
AUTH_SECRET=[新生成的密钥]
AUTH_URL=https://watermarkremover.top/api/auth
NEXTAUTH_URL=https://watermarkremover.top
NEXT_PUBLIC_WEB_URL=https://watermarkremover.top

# Google OAuth
AUTH_GOOGLE_ID=[您的Google Client ID]
AUTH_GOOGLE_SECRET=[您的Google Client Secret]
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=false

# PayPal Live
PAYPAL_CLIENT_ID=[Live Client ID]
PAYPAL_CLIENT_SECRET=[Live Client Secret]
PAYPAL_ENVIRONMENT=live

# 支付回调URL
NEXT_PUBLIC_PAY_SUCCESS_URL=https://watermarkremover.top/payment-success
NEXT_PUBLIC_PAY_FAIL_URL=https://watermarkremover.top/pricing?error=payment_failed
NEXT_PUBLIC_PAY_CANCEL_URL=https://watermarkremover.top/pricing?cancelled=true

# 数据库
DATABASE_URL=[您的Supabase连接字符串]

# API配置
WATERMARK_API_URL=https://api.textin.com/ai/service/v1/image/watermark_remove
WATERMARK_APP_ID=[您的API ID]
WATERMARK_SECRET_CODE=[您的API密钥]

# 管理员
ADMIN_EMAILS=<EMAIL>
```

## ✅ **部署后验证清单**

### **1. 认证功能验证**:
- [ ] Google登录正常工作
- [ ] 重定向到正确的生产域名
- [ ] 用户会话正常保持

### **2. 支付功能验证**:
- [ ] PayPal支付流程正常
- [ ] 支付成功回调正确
- [ ] 积分正确添加

### **3. API功能验证**:
- [ ] 去水印API正常工作
- [ ] 图片上传和处理正常
- [ ] 错误处理正确

### **4. 安全验证**:
- [ ] HTTPS强制重定向
- [ ] CSP头部正确设置
- [ ] 无unsafe-eval警告

## 🔍 **验证脚本**

部署后运行以下命令验证配置：

```bash
# 检查生产环境安全配置
curl -I https://watermarkremover.top | grep -i "content-security-policy"

# 检查认证端点
curl https://watermarkremover.top/api/auth/providers

# 检查sitemap
curl https://watermarkremover.top/sitemap.xml
```

## 🚨 **安全注意事项**

1. **绝不要**在客户端代码中暴露敏感密钥
2. **确保**所有NEXT_PUBLIC_开头的变量都是安全的
3. **定期轮换**AUTH_SECRET和API密钥
4. **监控**生产环境的错误日志

## 📞 **如果遇到问题**

### **常见问题**:
1. **Google登录失败** → 检查OAuth域名配置
2. **支付失败** → 检查PayPal环境设置
3. **API错误** → 检查去水印API配置
4. **HTTPS重定向问题** → 检查AUTH_URL设置

### **调试步骤**:
1. 检查Vercel部署日志
2. 验证环境变量设置
3. 测试各个功能模块
4. 查看浏览器控制台错误

---

**总结**: 主要需要修改认证URL、支付回调URL、Google OAuth域名配置，并确保PayPal切换到生产环境。其他配置基本可以保持不变。
