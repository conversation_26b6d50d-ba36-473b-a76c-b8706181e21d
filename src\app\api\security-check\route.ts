import { NextRequest, NextResponse } from 'next/server';
import { SECURITY_CONFIG } from '@/config/security';

export async function GET(request: NextRequest) {
  const checks = {
    https: request.url.startsWith('https://'),
    domain: SECURITY_CONFIG.ALLOWED_DOMAINS.includes(request.nextUrl.hostname),
    headers: {
      csp: request.headers.get('content-security-policy') !== null,
      xFrame: request.headers.get('x-frame-options') !== null,
      xContentType: request.headers.get('x-content-type-options') !== null,
    },
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json({
    status: 'ok',
    secure: checks.https && checks.domain,
    checks,
    message: checks.https && checks.domain ? 'Site is secure' : 'Security issues detected',
  });
}