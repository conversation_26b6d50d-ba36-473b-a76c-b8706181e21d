import { respData, respErr, respJson } from "@/lib/resp";

import { findUserByUuid } from "@/models/user";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { User } from "@/types/user";

export async function POST(req: Request): Promise<Response> {
  try {
    // 添加超时处理
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), 10000); // 10秒超时
    });

    const getUserInfo = async (): Promise<Response> => {
      const user_uuid = await getUserUuid();
      if (!user_uuid) {
        return respJson(-2, "no auth");
      }

      const dbUser = await findUserByUuid(user_uuid);
      if (!dbUser) {
        return respErr("user not exist");
      }

      const userCredits = await getUserCredits(user_uuid);

      const user = {
        ...(dbUser as unknown as User),
        credits: userCredits,
      };

      return respData(user);
    };

    const result = await Promise.race([
      getUserInfo(),
      timeoutPromise
    ]);

    return result;
  } catch (e) {
    console.log("get user info failed: ", e);
    if (e instanceof Error && e.message === 'Request timeout') {
      return respErr("Request timeout - please try again");
    }
    return respErr("get user info failed");
  }
}
