'use client'

import { Suspense, lazy, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Shield, BarChart3, Loader2 } from "lucide-react"

// 懒加载组件
const AdminManagement = lazy(() => import("@/components/admin/admin-management"))
const CreditsStats = lazy(() => import("@/components/admin/credits-stats"))

// 加载中组件
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-12">
    <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
    <span className="ml-2 text-gray-600">加载中...</span>
  </div>
)

export default function ManagementPage() {
  const [activeTab, setActiveTab] = useState("admins")

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">系统管理</h1>
          <p className="text-gray-600 dark:text-gray-400">
            管理系统管理员和查看积分统计数据
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
          <TabsTrigger value="admins" className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            管理员管理
          </TabsTrigger>
          <TabsTrigger value="credits" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            积分统计
          </TabsTrigger>
        </TabsList>

        <TabsContent value="admins" className="space-y-6">
          <Suspense fallback={<LoadingSpinner />}>
            <AdminManagement />
          </Suspense>
        </TabsContent>

        <TabsContent value="credits" className="space-y-6">
          <Suspense fallback={<LoadingSpinner />}>
            <CreditsStats />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
}
