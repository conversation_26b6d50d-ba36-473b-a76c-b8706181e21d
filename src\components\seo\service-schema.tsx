'use client'

import Script from 'next/script'

export default function ServiceSchema() {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top'
  
  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "AI Watermark Removal Service",
    "description": "Professional AI-powered watermark removal service that removes watermarks from images quickly and efficiently using advanced machine learning algorithms.",
    "provider": {
      "@type": "Organization",
      "name": "Watermark Remover",
      "url": baseUrl
    },
    "serviceType": "Image Processing",
    "category": "Digital Image Editing",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Watermark Removal Plans",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Free Trial",
            "description": "3 free credits to try our watermark removal service"
          },
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Basic Plan",
            "description": "50 credits for watermark removal"
          },
          "price": "4.99",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Pro Plan",
            "description": "200 credits for watermark removal"
          },
          "price": "14.99",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Premium Plan",
            "description": "500 credits for watermark removal"
          },
          "price": "29.99",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        }
      ]
    },
    "areaServed": {
      "@type": "Place",
      "name": "Worldwide"
    },
    "availableChannel": {
      "@type": "ServiceChannel",
      "serviceUrl": baseUrl,
      "serviceSmsNumber": null,
      "servicePhone": null,
      "availableLanguage": [
        {
          "@type": "Language",
          "name": "English",
          "alternateName": "en"
        },
        {
          "@type": "Language", 
          "name": "Chinese",
          "alternateName": "zh"
        },
        {
          "@type": "Language",
          "name": "French", 
          "alternateName": "fr"
        },
        {
          "@type": "Language",
          "name": "Portuguese",
          "alternateName": "pt"
        },
        {
          "@type": "Language",
          "name": "Russian",
          "alternateName": "ru"
        }
      ]
    },
    "review": [
      {
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "author": {
          "@type": "Person",
          "name": "Sarah Johnson"
        },
        "reviewBody": "Amazing tool! Removed watermarks from my photos perfectly. The AI technology is impressive and the results are professional quality."
      },
      {
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "author": {
          "@type": "Person",
          "name": "Mike Chen"
        },
        "reviewBody": "Fast and efficient watermark removal. The free trial was great and the paid plans are very reasonable. Highly recommended!"
      }
    ],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    }
  }

  return (
    <Script
      id="service-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(serviceSchema)
      }}
    />
  )
}
