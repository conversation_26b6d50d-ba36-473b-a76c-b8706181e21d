'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { MessageCircle, Star, Trash2, Eye, BarChart3 } from "lucide-react";
import { toast } from "sonner";
import moment from "moment";

interface User {
  uuid: string;
  nickname?: string;
  email?: string;
  avatar_url?: string;
}

interface Feedback {
  id: number;
  content: string;
  rating?: number;
  status: string;
  created_at: string;
  user_uuid?: string;
  user?: User;
}

interface FeedbackStats {
  total: number;
  byRating: { [key: number]: number };
  byStatus: { [key: string]: number };
}

export default function FeedbacksPage() {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [stats, setStats] = useState<FeedbackStats>({
    total: 0,
    byRating: {},
    byStatus: {},
  });
  const [loading, setLoading] = useState(true);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const statusColors: { [key: string]: string } = {
    created: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    processed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    closed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  };

  const statusLabels: { [key: string]: string } = {
    created: '已创建',
    processed: '已处理',
    closed: '已关闭',
  };

  const fetchFeedbacks = async (pageNum = 1) => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/feedbacks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page: pageNum,
          limit: 20,
        }),
      });

      const result = await response.json();
      if (result.code === 0) {
        setFeedbacks(result.data.feedbacks);
        setTotalPages(result.data.totalPages);
      } else {
        toast.error(result.message || '获取反馈列表失败');
      }
    } catch (error) {
      console.error('Failed to fetch feedbacks:', error);
      toast.error('获取反馈列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/feedbacks');
      const result = await response.json();
      if (result.code === 0) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  useEffect(() => {
    fetchStats();
    fetchFeedbacks(page);
  }, [page]);

  const handleUpdateStatus = async (feedbackId: number, status: string) => {
    try {
      const response = await fetch(`/api/admin/feedbacks/${feedbackId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      const result = await response.json();
      if (result.code === 0) {
        toast.success('状态更新成功');
        fetchFeedbacks(page);
        fetchStats();
      } else {
        toast.error(result.message || '状态更新失败');
      }
    } catch (error) {
      console.error('Update status failed:', error);
      toast.error('状态更新失败');
    }
  };

  const handleDeleteFeedback = async (feedbackId: number) => {
    try {
      const response = await fetch(`/api/admin/feedbacks/${feedbackId}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.code === 0) {
        toast.success('删除成功');
        fetchFeedbacks(page);
        fetchStats();
      } else {
        toast.error(result.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete feedback failed:', error);
      toast.error('删除失败');
    }
  };

  const renderStars = (rating?: number) => {
    if (!rating) return '-';
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm">({rating})</span>
      </div>
    );
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">反馈管理</h1>
          <p className="text-gray-600 dark:text-gray-400">
            管理用户提交的反馈，查看评分和处理状态
          </p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">总反馈数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">平均评分</p>
                <p className="text-2xl font-bold">
                  {Object.keys(stats.byRating).length > 0
                    ? (
                        Object.entries(stats.byRating).reduce(
                          (sum, [rating, count]) => sum + parseInt(rating) * count,
                          0
                        ) / stats.total
                      ).toFixed(1)
                    : '-'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-green-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">已处理</p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.byStatus.processed || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">待处理</p>
                <p className="text-2xl font-bold text-blue-600">
                  {stats.byStatus.created || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 反馈列表 */}
      <Card>
        <CardHeader>
          <CardTitle>反馈列表</CardTitle>
          <CardDescription>查看和管理用户提交的反馈</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : feedbacks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">暂无数据</div>
          ) : (
            <div className="space-y-4">
              {feedbacks.map((feedback) => (
                <Card key={feedback.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {feedback.user && (
                            <div className="flex items-center gap-2">
                              {feedback.user.avatar_url && (
                                <img
                                  src={feedback.user.avatar_url}
                                  alt={feedback.user.nickname || '用户'}
                                  className="w-8 h-8 rounded-full"
                                />
                              )}
                              <span className="font-medium">
                                {feedback.user.nickname || feedback.user.email || '匿名用户'}
                              </span>
                            </div>
                          )}
                          <Badge className={statusColors[feedback.status] || statusColors.created}>
                            {statusLabels[feedback.status] || feedback.status}
                          </Badge>
                        </div>
                        <div className="mb-2">
                          {renderStars(feedback.rating)}
                        </div>
                        <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                          {feedback.content}
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                          <span>提交时间: {moment(feedback.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedFeedback(feedback)}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              查看
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>反馈详情</DialogTitle>
                              <DialogDescription>
                                查看和管理用户反馈
                              </DialogDescription>
                            </DialogHeader>
                            {selectedFeedback && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">用户</label>
                                    <p className="text-sm text-gray-600">
                                      {selectedFeedback.user?.nickname ||
                                       selectedFeedback.user?.email ||
                                       '匿名用户'}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">评分</label>
                                    <div className="mt-1">
                                      {renderStars(selectedFeedback.rating)}
                                    </div>
                                  </div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">反馈内容</label>
                                  <p className="text-sm text-gray-600 whitespace-pre-wrap bg-gray-50 dark:bg-gray-800 p-3 rounded mt-1">
                                    {selectedFeedback.content}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">提交时间</label>
                                  <p className="text-sm text-gray-600">
                                    {moment(selectedFeedback.created_at).format('YYYY-MM-DD HH:mm:ss')}
                                  </p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Select
                                    value={selectedFeedback.status}
                                    onValueChange={(status) => handleUpdateStatus(selectedFeedback.id, status)}
                                  >
                                    <SelectTrigger className="w-32">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="created">已创建</SelectItem>
                                      <SelectItem value="processed">已处理</SelectItem>
                                      <SelectItem value="closed">已关闭</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button variant="destructive" size="sm">
                                        <Trash2 className="w-4 h-4 mr-1" />
                                        删除
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>确认删除</AlertDialogTitle>
                                        <AlertDialogDescription>
                                          此操作不可撤销。确定要删除这条反馈吗？
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>取消</AlertDialogCancel>
                                        <AlertDialogAction
                                          onClick={() => handleDeleteFeedback(selectedFeedback.id)}
                                        >
                                          删除
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                上一页
              </Button>
              <span className="text-sm text-gray-600">
                第 {page} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
