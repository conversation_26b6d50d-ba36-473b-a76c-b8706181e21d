{"editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "i18n-ally.localesPaths": ["src/i18n/messages"], "i18n-ally.keystyle": "nested", "kiroAgent.configureMCP": "Disabled", "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}}