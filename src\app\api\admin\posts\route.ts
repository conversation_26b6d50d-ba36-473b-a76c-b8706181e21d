import { respData, respErr } from "@/lib/resp";
import { getAllPosts, getPostsTotal } from "@/models/post";
import { isCurrentUserAdmin } from "@/services/admin";

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { page = 1, limit = 20, search } = await req.json();

    // 获取文章列表
    const posts = await getAllPosts(page, limit);
    const total = await getPostsTotal();

    return respData({
      posts: posts || [],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (e) {
    console.log("get admin posts failed: ", e);
    return respErr("获取文章列表失败");
  }
}
