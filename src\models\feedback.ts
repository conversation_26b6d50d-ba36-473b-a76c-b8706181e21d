import { feedbacks } from "@/db/schema";
import { db } from "@/db";
import { getUsersByUuids } from "./user";
import { desc, eq } from "drizzle-orm";

export async function insertFeedback(
  data: typeof feedbacks.$inferInsert
): Promise<typeof feedbacks.$inferSelect | undefined> {
  const [feedback] = await db().insert(feedbacks).values(data).returning();

  return feedback;
}

export async function findFeedbackById(
  id: number
): Promise<typeof feedbacks.$inferSelect | undefined> {
  const [feedback] = await db()
    .select()
    .from(feedbacks)
    .where(eq(feedbacks.id, id))
    .limit(1);

  return feedback;
}

export async function getFeedbacks(
  page: number = 1,
  limit: number = 50
): Promise<(typeof feedbacks.$inferSelect)[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(feedbacks)
    .orderBy(desc(feedbacks.created_at))
    .limit(limit)
    .offset(offset);

  if (!data || data.length === 0) {
    return [];
  }

  const user_uuids = Array.from(new Set(data.map((item) => item.user_uuid)));
  const users = await getUsersByUuids(user_uuids as string[]);

  return data.map((item) => {
    const user = users?.find((user) => user.uuid === item.user_uuid);
    return { ...item, user };
  });
}

export async function getFeedbacksTotal(): Promise<number | undefined> {
  const total = await db().$count(feedbacks);

  return total;
}

export async function updateFeedback(
  id: number,
  data: Partial<typeof feedbacks.$inferInsert>
): Promise<typeof feedbacks.$inferSelect | undefined> {
  const [feedback] = await db()
    .update(feedbacks)
    .set(data)
    .where(eq(feedbacks.id, id))
    .returning();

  return feedback;
}

export async function deleteFeedback(
  id: number
): Promise<boolean> {
  try {
    await db().delete(feedbacks).where(eq(feedbacks.id, id));
    return true;
  } catch (error) {
    console.error('Delete feedback failed:', error);
    return false;
  }
}

// 获取反馈统计数据
export async function getFeedbacksStats(): Promise<{
  total: number;
  byRating: { [key: number]: number };
  byStatus: { [key: string]: number };
}> {
  const allFeedbacks = await db().select().from(feedbacks);

  const total = allFeedbacks.length;
  const byRating: { [key: number]: number } = {};
  const byStatus: { [key: string]: number } = {};

  allFeedbacks.forEach(feedback => {
    // 统计评分
    if (feedback.rating) {
      byRating[feedback.rating] = (byRating[feedback.rating] || 0) + 1;
    }

    // 统计状态
    const status = feedback.status || 'unknown';
    byStatus[status] = (byStatus[status] || 0) + 1;
  });

  return {
    total,
    byRating,
    byStatus,
  };
}
