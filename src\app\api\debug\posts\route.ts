import { respData, respErr } from "@/lib/resp";
import { getAllPosts } from "@/models/post";
import { isCurrentUserAdmin } from "@/services/admin";

export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 获取所有文章（包括已删除的）
    const posts = await getAllPosts(1, 100);

    return respData({
      posts: posts || [],
      count: posts?.length || 0,
      message: "调试数据 - 所有文章"
    });
  } catch (e) {
    console.log("debug posts failed: ", e);
    return respErr("获取调试数据失败");
  }
}
