'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { useAppContext } from '@/contexts/app';
import { useTranslations } from 'next-intl';
import { CheckCircle, XCircle, CreditCard, User, Sparkles } from 'lucide-react';

export default function PaymentSuccessToast() {
  const searchParams = useSearchParams();
  const { user, setUser } = useAppContext();
  const t = useTranslations('payment');

  // 刷新用户信息的函数
  const refreshUserInfo = async () => {
    try {
      const resp = await fetch("/api/get-user-info", {
        method: "POST",
      });

      if (!resp.ok) {
        throw new Error("fetch user info failed with status: " + resp.status);
      }

      const { code, message, data } = await resp.json();
      if (code !== 0) {
        throw new Error(message);
      }

      setUser(data);
    } catch (e) {
      console.log("refresh user info failed:", e);
    }
  };

  useEffect(() => {
    const payment = searchParams.get('payment');
    const credits = searchParams.get('credits');
    const amount = searchParams.get('amount');
    const orderNo = searchParams.get('order_no');
    const error = searchParams.get('error');

    // 只有在有支付相关参数时才处理
    if (!payment && !error) {
      return;
    }

    if (payment === 'success') {
      // 显示支付成功提示
      const message = t('success.thank_you_message');

      toast.custom((toastId) => (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 shadow-lg backdrop-blur-sm max-w-md">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="relative">
                <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                <Sparkles className="w-3 h-3 text-yellow-500 absolute -top-1 -right-1 animate-pulse" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-green-800 dark:text-green-200 text-sm">
                  {message}
                </h3>
              </div>
              <p className="text-green-700 dark:text-green-300 text-xs mb-3">
                {t('success.credits_description', { credits: credits || '0' })}
              </p>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    window.location.href = '/i/user-center';
                    toast.dismiss(toastId);
                  }}
                  className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-lg transition-colors"
                >
                  <User className="w-3 h-3" />
                  {t('success.view_user_center')}
                </button>
                <button
                  onClick={() => toast.dismiss(toastId)}
                  className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-xs font-medium px-2 py-1.5 rounded transition-colors"
                >
                  {t('common.close')}
                </button>
              </div>
            </div>
          </div>
        </div>
      ), {
        duration: 8000,
      });

      // 刷新用户信息以更新积分显示
      refreshUserInfo();

      // 清理URL参数，避免刷新页面时重复显示
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('credits');
      url.searchParams.delete('amount');
      url.searchParams.delete('order_no');
      window.history.replaceState({}, '', url.toString());
    } else if (error === 'payment_failed') {
      // 显示支付失败提示
      const message = t('failure.payment_failed_title');

      toast.custom((toastId) => (
        <div className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 shadow-lg backdrop-blur-sm max-w-md">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <XCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-red-800 dark:text-red-200 text-sm">
                  {message}
                </h3>
              </div>
              <p className="text-red-700 dark:text-red-300 text-xs mb-3">
                {t('failure.payment_failed_description')}
              </p>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    window.location.reload();
                    toast.dismiss(toastId);
                  }}
                  className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-lg transition-colors"
                >
                  <CreditCard className="w-3 h-3" />
                  {t('failure.try_again')}
                </button>
                <button
                  onClick={() => toast.dismiss(toastId)}
                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-xs font-medium px-2 py-1.5 rounded transition-colors"
                >
                  {t('common.close')}
                </button>
              </div>
            </div>
          </div>
        </div>
      ), {
        duration: 10000,
      });

      // 清理URL参数，避免刷新页面时重复显示
      const url = new URL(window.location.href);
      url.searchParams.delete('error');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams, t]);

  return null;
}
