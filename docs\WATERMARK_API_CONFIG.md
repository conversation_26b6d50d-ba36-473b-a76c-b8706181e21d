# 去水印API配置文档

## 概述

本项目支持通过环境变量配置去水印API，目前主要支持TextIn API，也可以配置其他兼容的API服务。

## 环境变量配置

### 必需的环境变量

```bash
# 去水印API的URL地址
WATERMARK_API_URL=https://api.textin.com/ai/service/v1/image/watermark_remove

# API应用ID
WATERMARK_APP_ID=your_app_id_here

# API密钥
WATERMARK_SECRET_CODE=your_secret_code_here
```

### 配置步骤

1. **复制环境变量模板**
   ```bash
   cp .env.example .env.local
   ```

2. **编辑 `.env.local` 文件**
   ```bash
   # 替换为您的实际配置
   WATERMARK_API_URL=https://api.textin.com/ai/service/v1/image/watermark_remove
   WATERMARK_APP_ID=f0fee7f6142b26899544bf7a1486cb96
   WATERMARK_SECRET_CODE=6ebf2a8b1e05bc74589ed39432bb4fe1
   ```

3. **重启应用**
   ```bash
   npm run dev
   ```

## 支持的API提供商

### 1. TextIn API (默认)

**官网**: https://www.textin.com/
**API文档**: https://www.textin.com/document/watermark_remove

**配置示例**:
```bash
WATERMARK_API_URL=https://api.textin.com/ai/service/v1/image/watermark_remove
WATERMARK_APP_ID=your_textin_app_id
WATERMARK_SECRET_CODE=your_textin_secret_code
```

**请求格式**:
- Method: POST
- Content-Type: application/octet-stream
- Headers:
  - x-ti-app-id: 应用ID
  - x-ti-secret-code: 密钥

### 2. 自定义API

如果您使用其他去水印API服务，请确保API接口兼容以下格式：

**请求格式**:
```
POST /your-api-endpoint
Content-Type: application/octet-stream
x-ti-app-id: your_app_id
x-ti-secret-code: your_secret_code

[图片二进制数据]
```

**响应格式**:
```json
{
  "code": 200,
  "result": {
    "image": "base64_encoded_image_data"
  }
}
```

## 配置验证

应用启动时会自动验证配置：

1. **配置完整性检查**: 确保所有必需的环境变量都已设置
2. **API连通性测试**: 可选，在管理后台进行API测试
3. **错误处理**: 配置错误时会返回友好的错误信息

## 生产环境配置

### Vercel部署

在Vercel项目设置中添加环境变量：

1. 进入项目设置 → Environment Variables
2. 添加以下变量：
   - `WATERMARK_API_URL`
   - `WATERMARK_APP_ID`
   - `WATERMARK_SECRET_CODE`

### Docker部署

在docker-compose.yml中配置：

```yaml
services:
  app:
    environment:
      - WATERMARK_API_URL=https://api.textin.com/ai/service/v1/image/watermark_remove
      - WATERMARK_APP_ID=your_app_id
      - WATERMARK_SECRET_CODE=your_secret_code
```

## 安全注意事项

1. **敏感信息保护**: 
   - 不要将API密钥提交到代码仓库
   - 使用环境变量或密钥管理服务

2. **访问控制**:
   - 定期轮换API密钥
   - 监控API使用情况

3. **错误处理**:
   - 不要在客户端暴露API密钥
   - 记录API调用错误但不暴露敏感信息

## 故障排除

### 常见问题

1. **配置错误**
   ```
   错误: 去水印服务配置错误，请联系管理员
   解决: 检查环境变量是否正确设置
   ```

2. **API调用失败**
   ```
   错误: 去水印失败
   解决: 检查API密钥是否有效，网络是否正常
   ```

3. **积分不足**
   ```
   错误: 积分不足，请先充值
   解决: 用户需要购买积分或管理员手动添加积分
   ```

### 调试模式

开启调试日志：
```bash
DEBUG=watermark:* npm run dev
```

## API使用统计

可以在管理后台查看：
- API调用次数
- 成功/失败率
- 用户使用情况
- 积分消耗统计
