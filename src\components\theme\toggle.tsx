"use client";

import { BsMoonStars, BsSun } from "react-icons/bs";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";

export default function ThemeToggle() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isToggling, setIsToggling] = useState(false);
  const t = useTranslations('theme');

  // 避免水合不匹配
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center gap-x-2 px-2">
        <div className="relative p-2 rounded-full bg-gray-100 dark:bg-gray-800 transition-all duration-300">
          <div className="w-5 h-5" />
        </div>
      </div>
    );
  }

  const toggleTheme = () => {
    setIsToggling(true);

    // 添加页面过渡效果
    document.documentElement.style.transition = 'background-color 0.3s ease, color 0.3s ease';

    setTimeout(() => {
      const newTheme = resolvedTheme === "dark" ? "light" : "dark";
      setTheme(newTheme);
      setIsToggling(false);

      // 移除过渡效果
      setTimeout(() => {
        document.documentElement.style.transition = '';
      }, 300);
    }, 150);
  };

  const isDark = resolvedTheme === "dark";

  return (
    <div className="flex items-center gap-x-2 px-2">
      <button
        onClick={toggleTheme}
        disabled={isToggling}
        className={`
          relative p-2 rounded-full transition-all duration-300 group
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          dark:focus:ring-blue-400 dark:focus:ring-offset-gray-800
          ${isDark
            ? 'bg-slate-800 hover:bg-slate-700 shadow-lg border border-slate-600'
            : 'bg-yellow-100 hover:bg-yellow-200 shadow-md border border-yellow-200'
          }
          ${isToggling ? 'scale-95' : 'hover:scale-105 active:scale-95'}
        `}
        aria-label={isDark ? t('switch_to_light') : t('switch_to_dark')}
        title={isDark ? t('switch_to_light') : t('switch_to_dark')}
      >
        <div className="relative w-5 h-5">
          {/* 太阳图标 */}
          <BsSun
            className={`
              absolute inset-0 text-lg transition-all duration-300 transform
              ${isDark
                ? 'opacity-0 rotate-90 scale-0 text-yellow-400'
                : 'opacity-100 rotate-0 scale-100 text-yellow-600 group-hover:text-yellow-700'
              }
              ${isToggling ? 'animate-spin' : ''}
            `}
            width={20}
            height={20}
          />

          {/* 月亮图标 */}
          <BsMoonStars
            className={`
              absolute inset-0 text-lg transition-all duration-300 transform
              ${isDark
                ? 'opacity-100 rotate-0 scale-100 text-blue-300 group-hover:text-blue-200'
                : 'opacity-0 -rotate-90 scale-0 text-blue-600'
              }
              ${isToggling ? 'animate-pulse' : ''}
            `}
            width={20}
            height={20}
          />
        </div>

        {/* 背景光晕效果 */}
        <div className={`
          absolute inset-0 rounded-full transition-all duration-300 opacity-0 group-hover:opacity-100
          ${isDark
            ? 'bg-blue-400/20 shadow-lg shadow-blue-400/25'
            : 'bg-yellow-400/20 shadow-lg shadow-yellow-400/25'
          }
        `} />
      </button>
    </div>
  );
}
