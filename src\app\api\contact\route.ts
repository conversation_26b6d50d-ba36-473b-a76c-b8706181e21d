import { NextRequest, NextResponse } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { insertContact } from "@/models/contact";
import { getUserUuid } from "@/services/user";

export async function POST(req: NextRequest) {
  try {
    const { name, email, subject, message } = await req.json();

    // 验证必要字段
    if (!name || !email || !subject || !message) {
      return respErr("所有字段都是必填的");
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return respErr("邮箱格式不正确");
    }

    // 验证字段长度
    if (name.length > 255) {
      return respErr("姓名长度不能超过255个字符");
    }
    if (email.length > 255) {
      return respErr("邮箱长度不能超过255个字符");
    }
    if (subject.length > 500) {
      return respErr("主题长度不能超过500个字符");
    }
    if (message.length > 5000) {
      return respErr("消息长度不能超过5000个字符");
    }

    // 获取用户UUID（如果已登录）
    let user_uuid: string | null = null;
    try {
      user_uuid = await getUserUuid();
    } catch (error) {
      // 用户未登录，继续处理
    }

    // 获取客户端IP地址
    const forwarded = req.headers.get("x-forwarded-for");
    const ip_address = forwarded ? forwarded.split(",")[0] : req.headers.get("x-real-ip") || "unknown";

    // 获取用户代理
    const user_agent = req.headers.get("user-agent") || "unknown";

    // 创建联系记录
    const contactData = {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      subject: subject.trim(),
      message: message.trim(),
      status: "unread" as const,
      user_uuid,
      ip_address,
      user_agent,
      created_at: new Date(),
      updated_at: new Date(),
    };

    const contact = await insertContact(contactData);

    if (!contact) {
      return respErr("提交失败，请稍后重试");
    }

    return respData({
      id: contact.id,
      message: "您的消息已成功提交，我们会尽快回复您！",
    });
  } catch (error) {
    console.error("Contact form submission failed:", error);
    return respErr("提交失败，请稍后重试");
  }
}
