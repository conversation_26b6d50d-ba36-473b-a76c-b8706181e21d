import { respData, respErr } from "@/lib/resp";
import { isCurrentUserAdmin } from "@/services/admin";
import { cleanupExpiredCredits } from "@/services/credit";

/**
 * 管理员清理过期积分
 */
export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    console.log("Admin triggered expired credits cleanup");
    
    // 执行过期积分清理
    const deletedCount = await cleanupExpiredCredits();

    return respData({ 
      message: "过期积分清理完成", 
      deletedCount 
    });
  } catch (e) {
    console.log("expired credits cleanup failed: ", e);
    return respErr("过期积分清理失败");
  }
}
