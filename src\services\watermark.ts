/**
 * 去水印服务配置
 */

export interface WatermarkConfig {
  apiUrl: string;
  appId: string;
  secretCode: string;
}

/**
 * 获取去水印API配置
 */
export function getWatermarkConfig(): WatermarkConfig {
  const config = {
    apiUrl: process.env.WATERMARK_API_URL || "https://api.textin.com/ai/service/v1/image/watermark_remove",
    appId: process.env.WATERMARK_APP_ID || "",
    secretCode: process.env.WATERMARK_SECRET_CODE || "",
  };

  return config;
}

/**
 * 验证去水印API配置是否完整
 */
export function validateWatermarkConfig(config: WatermarkConfig): boolean {
  return !!(config.apiUrl && config.appId && config.secretCode);
}

/**
 * 获取配置错误信息
 */
export function getConfigErrorMessage(config: WatermarkConfig): string {
  const missing = [];
  
  if (!config.apiUrl) missing.push("WATERMARK_API_URL");
  if (!config.appId) missing.push("WATERMARK_APP_ID");
  if (!config.secretCode) missing.push("WATERMARK_SECRET_CODE");
  
  if (missing.length > 0) {
    return `去水印API配置缺失: ${missing.join(", ")}。请检查环境变量配置。`;
  }
  
  return "";
}

/**
 * 支持的去水印API提供商
 */
export enum WatermarkProvider {
  TEXTIN = "textin",
  // 可以扩展其他提供商
  // CUSTOM = "custom",
}

/**
 * 根据提供商获取默认配置
 */
export function getDefaultConfigByProvider(provider: WatermarkProvider): Partial<WatermarkConfig> {
  switch (provider) {
    case WatermarkProvider.TEXTIN:
      return {
        apiUrl: "https://api.textin.com/ai/service/v1/image/watermark_remove",
      };
    default:
      return {};
  }
}
