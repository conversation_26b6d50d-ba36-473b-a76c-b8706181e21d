import { contacts } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, count } from "drizzle-orm";

export enum ContactStatus {
  Unread = "unread",
  Read = "read", 
  Replied = "replied",
  Closed = "closed",
}

export async function insertContact(
  data: typeof contacts.$inferInsert
): Promise<typeof contacts.$inferSelect | undefined> {
  const [contact] = await db().insert(contacts).values(data).returning();
  return contact;
}

export async function findContactById(
  id: number
): Promise<typeof contacts.$inferSelect | undefined> {
  const [contact] = await db()
    .select()
    .from(contacts)
    .where(eq(contacts.id, id))
    .limit(1);
  return contact;
}

export async function getContacts(
  page: number = 1,
  limit: number = 20,
  status?: string
): Promise<(typeof contacts.$inferSelect)[] | undefined> {
  const offset = (page - 1) * limit;

  const baseQuery = db()
    .select()
    .from(contacts)
    .orderBy(desc(contacts.created_at))
    .limit(limit)
    .offset(offset);

  const data = status
    ? await baseQuery.where(eq(contacts.status, status))
    : await baseQuery;

  return data || [];
}

export async function getContactsTotal(status?: string): Promise<number> {
  const baseQuery = db().select({ count: count() }).from(contacts);

  const result = status
    ? await baseQuery.where(eq(contacts.status, status))
    : await baseQuery;

  return result[0]?.count || 0;
}

export async function updateContact(
  id: number,
  data: Partial<typeof contacts.$inferInsert>
): Promise<typeof contacts.$inferSelect | undefined> {
  const [contact] = await db()
    .update(contacts)
    .set({ ...data, updated_at: new Date() })
    .where(eq(contacts.id, id))
    .returning();
  return contact;
}

export async function markContactAsRead(
  id: number
): Promise<typeof contacts.$inferSelect | undefined> {
  return updateContact(id, { status: ContactStatus.Read });
}

export async function replyToContact(
  id: number,
  reply: string,
  repliedBy: string
): Promise<typeof contacts.$inferSelect | undefined> {
  return updateContact(id, {
    status: ContactStatus.Replied,
    admin_reply: reply,
    replied_at: new Date(),
    replied_by: repliedBy,
  });
}

export async function closeContact(
  id: number
): Promise<typeof contacts.$inferSelect | undefined> {
  return updateContact(id, { status: ContactStatus.Closed });
}

// 获取联系我们统计数据
export async function getContactsStats(): Promise<{
  total: number;
  unread: number;
  read: number;
  replied: number;
  closed: number;
}> {
  const [total, unread, read, replied, closed] = await Promise.all([
    getContactsTotal(),
    getContactsTotal(ContactStatus.Unread),
    getContactsTotal(ContactStatus.Read),
    getContactsTotal(ContactStatus.Replied),
    getContactsTotal(ContactStatus.Closed),
  ]);

  return {
    total,
    unread,
    read,
    replied,
    closed,
  };
}
