<!DOCTYPE html>
<html>
<head>
    <title>测试图片上传</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0;
        }
        .result { 
            margin: 20px 0; 
            padding: 10px; 
            background: #f5f5f5; 
        }
    </style>
</head>
<body>
    <h1>Blog图片上传测试</h1>
    
    <div class="upload-area">
        <input type="file" id="fileInput" accept="image/*">
        <button onclick="uploadFile()">上传图片</button>
    </div>
    
    <div id="result" class="result" style="display:none;">
        <h3>上传结果：</h3>
        <pre id="resultText"></pre>
        <img id="resultImage" style="max-width: 300px; margin-top: 10px;">
    </div>

    <script>
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                document.getElementById('resultText').textContent = JSON.stringify(result, null, 2);
                document.getElementById('result').style.display = 'block';
                
                if (result.code === 0) {
                    document.getElementById('resultImage').src = result.data.url;
                    document.getElementById('resultImage').style.display = 'block';
                } else {
                    document.getElementById('resultImage').style.display = 'none';
                }
            } catch (error) {
                document.getElementById('resultText').textContent = '上传失败: ' + error.message;
                document.getElementById('result').style.display = 'block';
            }
        }
    </script>
</body>
</html>
