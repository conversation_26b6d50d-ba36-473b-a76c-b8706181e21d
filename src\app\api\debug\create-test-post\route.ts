import { respData, respErr } from "@/lib/resp";
import { insertPost, PostStatus } from "@/models/post";
import { isCurrentUserAdmin } from "@/services/admin";
import { getUuid } from "@/lib/hash";

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 创建测试文章
    const testPost = {
      uuid: getUuid(),
      created_at: new Date(),
      status: PostStatus.Online, // 确保状态为online
      title: "测试文章 - 水印去除技巧",
      slug: "test-watermark-removal-tips",
      locale: "zh",
      description: "这是一篇测试文章，介绍水印去除的基本技巧和方法。",
      cover_url: "/uploads/blog/test-cover.jpg",
      author_name: "管理员",
      author_avatar_url: null,
      content: `# 水印去除技巧

这是一篇测试文章，用于验证blog功能是否正常工作。

## 主要内容

1. **基础知识**: 了解水印的类型和特点
2. **工具介绍**: 推荐几款优秀的水印去除工具
3. **操作步骤**: 详细的操作指南
4. **注意事项**: 使用过程中需要注意的问题

## 总结

通过本文的介绍，相信大家对水印去除有了更深入的了解。

---

*这是一篇自动生成的测试文章*`,
    };

    const result = await insertPost(testPost);

    return respData({
      success: true,
      post: result,
      message: "测试文章创建成功"
    });
  } catch (e) {
    console.log("create test post failed: ", e);
    return respErr("创建测试文章失败: " + e.message);
  }
}
