"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { SiGoogle } from "react-icons/si";
import { Button } from "@/components/ui/button";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { Link } from "@/i18n/navigation";

// 现代化登录表单组件，与模态框风格保持一致
export default function SignForm({ className, ...props }: React.ComponentPropsWithoutRef<"div">) {
  const t = useTranslations();
  return (
    <div className={"flex flex-col gap-6 " + (className || "")} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">{t('sign_modal.sign_in_title')}</CardTitle>
          <p className="text-sm text-muted-foreground mt-2">
            {t('sign_modal.sign_in_description')}
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Google登录按钮 - 与模态框风格一致 */}
          {process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" && (
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full flex items-center justify-center gap-3 py-3 border-2 border-blue-300 hover:border-blue-500 hover:bg-blue-50 transition-all"
                onClick={() => signIn("google")}
              >
                <SiGoogle className="w-5 h-5 text-blue-600" />
                <span className="font-medium">{t('sign_modal.google_sign_in')}</span>
              </Button>
              <div className="text-center text-xs text-gray-500">
                <p>{t('sign_modal.google_redirect_notice')}</p>
              </div>
            </div>
          )}

          {/* Google安全说明 - 与模态框一致 */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-sm text-blue-700">
              <SiGoogle className="w-4 h-4" />
              <span className="font-medium">{t('sign_modal.google_security_notice')}</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">{t('sign_modal.google_security_description')}</p>
          </div>
        </CardContent>
      </Card>

      {/* 服务条款和隐私政策 */}
      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        {t('sign_modal.terms_agreement')} <Link href="/terms-of-service" target="_blank">{t('sign_modal.terms_of_service')}</Link> {t('sign_modal.and')} <Link href="/privacy-policy" target="_blank">{t('sign_modal.privacy_policy')}</Link>.
      </div>
    </div>
  );
}
