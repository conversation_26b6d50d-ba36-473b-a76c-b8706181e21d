#!/usr/bin/env node

/**
 * 登录流程测试脚本
 * 模拟登录流程，检查各个步骤是否正常
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env' });

const https = require('https');
const http = require('http');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

console.log('🧪 开始登录流程测试...');
console.log(`测试网站: ${SITE_URL}`);

// 1. 测试登录页面加载
function testSignInPageLoad() {
  return new Promise((resolve) => {
    console.log('\n📄 测试登录页面加载:');
    
    const url = `${SITE_URL}/auth/signin`;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      console.log(`📊 状态码: ${res.statusCode}`);
      
      if (res.statusCode === 200) {
        console.log('✅ 登录页面加载成功');
        resolve(true);
      } else {
        console.log('❌ 登录页面加载失败');
        resolve(false);
      }
    });
    
    req.on('error', (err) => {
      console.log(`❌ 登录页面请求失败: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ 登录页面请求超时');
      resolve(false);
    });
  });
}

// 2. 测试Google OAuth配置
function testGoogleOAuthConfig() {
  return new Promise((resolve) => {
    console.log('\n🔐 测试Google OAuth配置:');
    
    const url = `${SITE_URL}/api/auth/providers`;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const providers = JSON.parse(data);
          
          if (providers.google) {
            console.log('✅ Google提供商配置正确');
            console.log(`   ID: ${providers.google.id}`);
            console.log(`   名称: ${providers.google.name}`);
            console.log(`   类型: ${providers.google.type}`);
            resolve(true);
          } else {
            console.log('❌ Google提供商未配置');
            console.log('可用提供商:', Object.keys(providers));
            resolve(false);
          }
        } catch (e) {
          console.log('❌ 提供商数据解析失败');
          console.log('响应数据:', data.substring(0, 200));
          resolve(false);
        }
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 提供商配置请求失败: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ 提供商配置请求超时');
      resolve(false);
    });
  });
}

// 3. 测试CSRF令牌
function testCSRFToken() {
  return new Promise((resolve) => {
    console.log('\n🛡️ 测试CSRF令牌:');
    
    const url = `${SITE_URL}/api/auth/csrf`;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const csrf = JSON.parse(data);
          
          if (csrf.csrfToken) {
            console.log('✅ CSRF令牌获取成功');
            console.log(`   令牌长度: ${csrf.csrfToken.length}`);
            resolve(true);
          } else {
            console.log('❌ CSRF令牌获取失败');
            console.log('响应数据:', data);
            resolve(false);
          }
        } catch (e) {
          console.log('❌ CSRF令牌解析失败');
          console.log('响应数据:', data.substring(0, 200));
          resolve(false);
        }
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ CSRF令牌请求失败: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ CSRF令牌请求超时');
      resolve(false);
    });
  });
}

// 4. 测试会话状态
function testSessionStatus() {
  return new Promise((resolve) => {
    console.log('\n👤 测试会话状态:');
    
    const url = `${SITE_URL}/api/auth/session`;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const session = JSON.parse(data);
          
          if (session.user) {
            console.log('✅ 用户已登录');
            console.log(`   用户: ${session.user.email || session.user.name}`);
            resolve('logged_in');
          } else {
            console.log('✅ 用户未登录（正常状态）');
            resolve('not_logged_in');
          }
        } catch (e) {
          console.log('❌ 会话数据解析失败');
          console.log('响应数据:', data.substring(0, 200));
          resolve('error');
        }
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 会话状态请求失败: ${err.message}`);
      resolve('error');
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ 会话状态请求超时');
      resolve('error');
    });
  });
}

// 5. 生成测试报告
function generateTestReport(pageLoad, oauthConfig, csrfToken, sessionStatus) {
  console.log('\n📋 登录流程测试报告:');
  console.log('=' * 50);
  
  let score = 0;
  const issues = [];
  
  if (pageLoad) {
    score += 25;
    console.log('✅ 登录页面加载: 正常');
  } else {
    issues.push('登录页面加载失败');
    console.log('❌ 登录页面加载: 失败');
  }
  
  if (oauthConfig) {
    score += 25;
    console.log('✅ Google OAuth配置: 正常');
  } else {
    issues.push('Google OAuth配置异常');
    console.log('❌ Google OAuth配置: 异常');
  }
  
  if (csrfToken) {
    score += 25;
    console.log('✅ CSRF令牌: 正常');
  } else {
    issues.push('CSRF令牌获取失败');
    console.log('❌ CSRF令牌: 失败');
  }
  
  if (sessionStatus !== 'error') {
    score += 25;
    console.log('✅ 会话管理: 正常');
  } else {
    issues.push('会话管理异常');
    console.log('❌ 会话管理: 异常');
  }
  
  console.log(`\n📊 登录流程健康度: ${score}/100`);
  
  if (score === 100) {
    console.log('🎉 登录流程完全正常！');
  } else if (score >= 75) {
    console.log('✅ 登录流程基本正常');
  } else if (score >= 50) {
    console.log('⚠️ 登录流程有问题');
  } else {
    console.log('❌ 登录流程严重异常');
  }
  
  if (issues.length > 0) {
    console.log('\n🔧 发现的问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  console.log('\n💡 用户操作建议:');
  if (score >= 75) {
    console.log('1. 清除浏览器缓存和Cookie');
    console.log('2. 尝试无痕模式登录');
    console.log('3. 检查浏览器控制台是否有JavaScript错误');
    console.log('4. 确保浏览器允许弹窗（Google登录需要）');
  } else {
    console.log('1. 联系技术支持');
    console.log('2. 提供浏览器控制台错误信息');
    console.log('3. 尝试不同浏览器');
  }
  
  return score;
}

// 主函数
async function main() {
  try {
    const pageLoad = await testSignInPageLoad();
    const oauthConfig = await testGoogleOAuthConfig();
    const csrfToken = await testCSRFToken();
    const sessionStatus = await testSessionStatus();
    
    const score = generateTestReport(pageLoad, oauthConfig, csrfToken, sessionStatus);
    
    console.log('\n🔍 调试信息:');
    console.log(`AUTH_GOOGLE_ID: ${process.env.AUTH_GOOGLE_ID ? '已设置' : '未设置'}`);
    console.log(`NEXT_PUBLIC_AUTH_GOOGLE_ENABLED: ${process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED}`);
    
    process.exit(score < 75 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ 登录流程测试失败:', error.message);
    process.exit(1);
  }
}

main();
