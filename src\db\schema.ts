import {
  pgTable,
  serial,
  varchar,
  text,
  boolean,
  integer,
  timestamp,
  unique,
  uniqueIndex,
} from "drizzle-orm/pg-core";

// Users table
export const users = pgTable(
  "users",
  {
    id: integer().primaryKey().generatedAlwaysAsIdentity(),
    uuid: varchar({ length: 255 }).notNull().unique(),
    email: varchar({ length: 255 }).notNull(),
    created_at: timestamp({ withTimezone: true }),
    nickname: varchar({ length: 255 }),
    avatar_url: varchar({ length: 255 }),
    locale: varchar({ length: 50 }),
    signin_type: varchar({ length: 50 }),
    signin_ip: varchar({ length: 255 }),
    signin_provider: varchar({ length: 50 }),
    signin_openid: varchar({ length: 255 }),
    invite_code: varchar({ length: 255 }).notNull().default(""),
    updated_at: timestamp({ withTimezone: true }),
    invited_by: varchar({ length: 255 }).notNull().default(""),
    is_affiliate: boolean().notNull().default(false),
    is_admin: boolean().notNull().default(false),
  },
  (table) => [
    uniqueIndex("email_provider_unique_idx").on(
      table.email,
      table.signin_provider
    ),
  ]
);

// Orders table
export const orders = pgTable("orders", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  order_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp({ withTimezone: true }),
  user_uuid: varchar({ length: 255 }).notNull().default(""),
  user_email: varchar({ length: 255 }).notNull().default(""),
  amount: integer().notNull(),
  interval: varchar({ length: 50 }),
  expired_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }).notNull(),
  stripe_session_id: varchar({ length: 255 }),
  payment_provider: varchar({ length: 50 }).default('stripe'), // 支付提供商
  payment_session_id: varchar({ length: 255 }), // 通用支付会话ID
  credits: integer().notNull(),
  currency: varchar({ length: 50 }),
  sub_id: varchar({ length: 255 }),
  sub_interval_count: integer(),
  sub_cycle_anchor: integer(),
  sub_period_end: integer(),
  sub_period_start: integer(),
  sub_times: integer(),
  product_id: varchar({ length: 255 }),
  product_name: varchar({ length: 255 }),
  valid_months: integer(),
  order_detail: text(),
  paid_at: timestamp({ withTimezone: true }),
  paid_email: varchar({ length: 255 }),
  paid_detail: text(),
});

// API Keys table
export const apikeys = pgTable("apikeys", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  api_key: varchar({ length: 255 }).notNull().unique(),
  title: varchar({ length: 100 }),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
});

// Credits table
export const credits = pgTable("credits", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  trans_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp({ withTimezone: true }),
  user_uuid: varchar({ length: 255 }).notNull(),
  trans_type: varchar({ length: 50 }).notNull(),
  credits: integer().notNull(),
  order_no: varchar({ length: 255 }),
  expired_at: timestamp({ withTimezone: true }),
});

// Posts table
export const posts = pgTable("posts", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  slug: varchar({ length: 255 }),
  title: varchar({ length: 255 }),
  description: text(),
  content: text(),
  created_at: timestamp({ withTimezone: true }),
  updated_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
  cover_url: varchar({ length: 255 }),
  author_name: varchar({ length: 255 }),
  author_avatar_url: varchar({ length: 255 }),
  locale: varchar({ length: 50 }),
});

// Affiliates table
export const affiliates = pgTable("affiliates", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }).notNull().default(""),
  invited_by: varchar({ length: 255 }).notNull(),
  paid_order_no: varchar({ length: 255 }).notNull().default(""),
  paid_amount: integer().notNull().default(0),
  reward_percent: integer().notNull().default(0),
  reward_amount: integer().notNull().default(0),
});

// Bills table - 账单消费记录
export const bills = pgTable("bills", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  bill_no: varchar({ length: 255 }).notNull().unique(), // 账单号
  created_at: timestamp({ withTimezone: true }).notNull(), // 创建时间
  user_uuid: varchar({ length: 255 }).notNull(), // 用户UUID
  user_email: varchar({ length: 255 }).notNull(), // 用户邮箱
  transaction_type: varchar({ length: 50 }).notNull(), // 交易类型：payment, refund, subscription
  purpose: varchar({ length: 100 }).notNull(), // 用途：credits_purchase, subscription_monthly, subscription_yearly
  order_no: varchar({ length: 255 }), // 关联订单号
  payment_method: varchar({ length: 50 }), // 支付方式：paypal, stripe, alipay等
  amount: integer().notNull(), // 金额（分）
  currency: varchar({ length: 10 }).notNull().default('usd'), // 货币
  credits: integer().default(0), // 获得的积分数量
  status: varchar({ length: 50 }).notNull().default('completed'), // 状态：completed, pending, failed, refunded
  description: text(), // 描述
  metadata: text(), // 额外的元数据（JSON格式）
});

// Feedbacks table
export const feedbacks = pgTable("feedbacks", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
  user_uuid: varchar({ length: 255 }),
  content: text(),
  rating: integer(),
});

// Contacts table
export const contacts = pgTable("contacts", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: varchar({ length: 255 }).notNull(),
  email: varchar({ length: 255 }).notNull(),
  subject: varchar({ length: 500 }).notNull(),
  message: text().notNull(),
  status: varchar({ length: 50 }).notNull().default('unread'), // unread, read, replied, closed
  admin_reply: text(),
  replied_at: timestamp({ withTimezone: true }),
  replied_by: varchar({ length: 255 }),
  created_at: timestamp({ withTimezone: true }).defaultNow(),
  updated_at: timestamp({ withTimezone: true }).defaultNow(),
  user_uuid: varchar({ length: 255 }), // 如果是登录用户提交的
  ip_address: varchar({ length: 45 }), // 记录IP地址
  user_agent: text(), // 记录用户代理
});

// System logs table
export const systemLogs = pgTable("system_logs", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  level: varchar({ length: 20 }).notNull().default('info'), // info, warn, error, debug
  action: varchar({ length: 100 }).notNull(), // 操作类型
  message: text().notNull(), // 日志消息
  details: text(), // 详细信息 (JSON格式)
  user_uuid: varchar({ length: 255 }), // 操作用户
  user_email: varchar({ length: 255 }), // 操作用户邮箱
  ip_address: varchar({ length: 45 }), // IP地址
  user_agent: text(), // 用户代理
  module: varchar({ length: 50 }), // 模块名称 (user, order, feedback, contact, etc.)
  resource_id: varchar({ length: 255 }), // 相关资源ID
  created_at: timestamp({ withTimezone: true }).defaultNow(),
});
