#!/usr/bin/env node

/**
 * Next.js 15 参数修复脚本
 * 检查并修复所有使用params的组件，确保兼容Next.js 15的Promise类型
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 检查Next.js 15参数兼容性...');

// 需要检查的目录
const checkDirs = [
  'src/app',
];

// 递归查找所有tsx文件
function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (item.endsWith('.tsx') && (item === 'page.tsx' || item === 'layout.tsx')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// 检查文件中的params使用
function checkParamsUsage(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  // 检查是否有params参数但没有使用Promise类型
  const paramsRegex = /params\s*:\s*\{\s*[^}]+\s*\}/g;
  const promiseParamsRegex = /params\s*:\s*Promise<\{[^}]+\}>/g;
  
  const hasParams = paramsRegex.test(content);
  const hasPromiseParams = promiseParamsRegex.test(content);
  
  if (hasParams && !hasPromiseParams) {
    // 检查是否是服务器组件
    const isServerComponent = !content.includes("'use client'") && !content.includes('"use client"');
    
    if (isServerComponent) {
      issues.push({
        type: 'params_not_promise',
        message: '服务器组件的params应该是Promise类型',
        file: filePath
      });
    }
  }
  
  // 检查是否有直接访问params属性而没有await
  if (hasParams && content.includes('params.')) {
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      if (line.includes('params.') && !line.includes('await params') && !line.includes('const {') && !line.includes('= params')) {
        issues.push({
          type: 'direct_params_access',
          message: `第${index + 1}行: 直接访问params属性，应该先await`,
          file: filePath,
          line: index + 1,
          content: line.trim()
        });
      }
    });
  }
  
  return issues;
}

// 主函数
function main() {
  let totalIssues = 0;
  
  for (const dir of checkDirs) {
    if (!fs.existsSync(dir)) {
      console.log(`⚠️ 目录不存在: ${dir}`);
      continue;
    }
    
    console.log(`\n📁 检查目录: ${dir}`);
    const files = findTsxFiles(dir);
    
    for (const file of files) {
      const issues = checkParamsUsage(file);
      
      if (issues.length > 0) {
        console.log(`\n❌ ${file}:`);
        issues.forEach(issue => {
          console.log(`   - ${issue.message}`);
          if (issue.line) {
            console.log(`     第${issue.line}行: ${issue.content}`);
          }
        });
        totalIssues += issues.length;
      } else {
        console.log(`✅ ${file}`);
      }
    }
  }
  
  console.log(`\n📊 检查完成:`);
  console.log(`   总问题数: ${totalIssues}`);
  
  if (totalIssues === 0) {
    console.log('🎉 所有文件都兼容Next.js 15!');
  } else {
    console.log('\n💡 修复建议:');
    console.log('1. 将params类型改为Promise<{...}>');
    console.log('2. 在使用params前添加await');
    console.log('3. 确保组件是async函数');
    
    console.log('\n🔧 示例修复:');
    console.log('修复前:');
    console.log('  export default function Page({ params }: { params: { id: string } }) {');
    console.log('    const { id } = params;');
    console.log('  }');
    console.log('\n修复后:');
    console.log('  export default async function Page({ params }: { params: Promise<{ id: string }> }) {');
    console.log('    const { id } = await params;');
    console.log('  }');
  }
  
  process.exit(totalIssues > 0 ? 1 : 0);
}

main();
