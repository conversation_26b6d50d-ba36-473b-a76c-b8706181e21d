import { NextResponse } from 'next/server';
import { SECURITY_CONFIG, generateCSP } from '@/config/security';

export function addSecurityHeaders(response: NextResponse) {
  // 添加所有安全头
  Object.entries(SECURITY_CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  // 添加CSP头
  response.headers.set('Content-Security-Policy', generateCSP());
  
  return response;
}

export function isSecureRequest(request: Request): boolean {
  const url = new URL(request.url);
  return url.protocol === 'https:' || url.hostname === 'localhost';
}

export function isDomainAllowed(domain: string): boolean {
  return SECURITY_CONFIG.ALLOWED_DOMAINS.some(allowed => 
    domain === allowed || domain.endsWith(`.${allowed}`)
  );
}