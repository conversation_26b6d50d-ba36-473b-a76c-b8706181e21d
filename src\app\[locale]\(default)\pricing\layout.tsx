import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale })
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top'
  const localePrefix = locale === 'en' ? '' : `/${locale}`
  const canonicalUrl = `${baseUrl}${localePrefix}/pricing`

  return {
    title: 'Pricing Plans - Watermark Remover',
    description: 'Choose the perfect plan for your watermark removal needs. Flexible pricing with monthly and yearly options. Start with 3 free credits.',
    keywords: 'watermark removal pricing, AI image editing plans, subscription plans, watermark remover cost',
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: 'Pricing Plans - Watermark Remover',
      description: 'Choose the perfect plan for your watermark removal needs. Flexible pricing with monthly and yearly options.',
      url: canonicalUrl,
      type: 'website',
      images: [
        {
          url: `${baseUrl}/logo.png`,
          width: 1200,
          height: 630,
          alt: 'Watermark Remover Pricing',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: 'Pricing Plans - Watermark Remover',
      description: 'Choose the perfect plan for your watermark removal needs. Flexible pricing with monthly and yearly options.',
      images: [`${baseUrl}/logo.png`],
    },
  }
}

export default function PricingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
