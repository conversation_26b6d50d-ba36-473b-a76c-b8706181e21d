#!/usr/bin/env node

/**
 * Google安全风险审计脚本
 * 根据Google反欺骗政策全面检查网站安全风险
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env' });

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

console.log('🔒 开始Google安全风险审计...');
console.log(`审计网站: ${SITE_URL}`);

// 1. 检查模拟登录界面风险
function checkPhishingRisk(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🎣 检查模拟登录界面风险:');
    
    const url = `${siteUrl}/auth/signin`;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const risks = [];
        
        // 检查是否有伪装的Google登录界面
        const hasGoogleBranding = html.match(/google/gi) || [];
        const hasOfficialGoogleDomain = html.includes('accounts.google.com');
        const hasNextAuthProvider = html.includes('next-auth') || html.includes('NextAuth');
        
        console.log(`📊 页面包含"Google"文本: ${hasGoogleBranding.length}次`);
        console.log(`🔗 使用官方Google域名: ${hasOfficialGoogleDomain ? '是' : '否'}`);
        console.log(`🔐 使用NextAuth框架: ${hasNextAuthProvider ? '是' : '否'}`);
        
        // 检查是否有可疑的登录表单
        const hasPasswordInput = html.includes('type="password"');
        const hasEmailInput = html.includes('type="email"');
        const hasCustomLoginForm = hasPasswordInput && hasEmailInput;
        
        if (hasCustomLoginForm) {
          risks.push('发现自定义登录表单，可能被误认为钓鱼');
          console.log('⚠️ 发现自定义登录表单');
        } else {
          console.log('✅ 未发现自定义登录表单');
        }
        
        // 检查是否正确使用OAuth
        if (hasOfficialGoogleDomain && hasNextAuthProvider) {
          console.log('✅ 正确使用官方OAuth流程');
        } else if (!hasOfficialGoogleDomain && hasGoogleBranding.length > 0) {
          risks.push('使用Google品牌但未重定向到官方域名');
          console.log('❌ 使用Google品牌但未重定向到官方域名');
        }
        
        resolve(risks);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 登录页面检查失败: ${err.message}`);
      resolve(['登录页面检查失败']);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 登录页面检查超时');
      resolve(['登录页面检查超时']);
    });
  });
}

// 2. 检查One Tap登录风险
function checkOneTapRisk() {
  console.log('\n👆 检查One Tap登录风险:');
  
  const risks = [];
  
  // 检查是否启用了One Tap
  const oneTapEnabled = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED;
  
  if (oneTapEnabled === 'true') {
    console.log('⚠️ Google One Tap已启用');
    
    // 检查是否有隐私政策
    const privacyPolicyExists = fs.existsSync('src/app/[locale]/(legal)/privacy-policy/page.tsx');
    if (!privacyPolicyExists) {
      risks.push('启用One Tap但缺少隐私政策页面');
      console.log('❌ 缺少隐私政策页面');
    } else {
      console.log('✅ 隐私政策页面存在');
    }
    
    // 检查是否有用户协议
    const termsExists = fs.existsSync('src/app/[locale]/(legal)/terms-of-service/page.tsx');
    if (!termsExists) {
      risks.push('启用One Tap但缺少用户协议页面');
      console.log('❌ 缺少用户协议页面');
    } else {
      console.log('✅ 用户协议页面存在');
    }
    
  } else {
    console.log('✅ Google One Tap已禁用（安全）');
  }
  
  return risks;
}

// 3. 检查CDN和外部脚本风险
function checkCDNRisk(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n📦 检查CDN和外部脚本风险:');
    
    const protocol = siteUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const risks = [];
        
        // 检查外部脚本
        const externalScripts = html.match(/<script[^>]*src\s*=\s*["']https?:\/\/[^"']+/gi) || [];
        console.log(`📊 外部脚本数量: ${externalScripts.length}`);
        
        const trustedDomains = [
          'vercel.live',
          'googleapis.com',
          'gstatic.com',
          'google.com',
          'accounts.google.com',
          'ssl.gstatic.com'
        ];
        
        const untrustedScripts = [];
        externalScripts.forEach(script => {
          const isTrusted = trustedDomains.some(domain => script.includes(domain));
          if (!isTrusted) {
            untrustedScripts.push(script);
          }
        });
        
        if (untrustedScripts.length > 0) {
          risks.push(`发现${untrustedScripts.length}个不受信任的外部脚本`);
          console.log('⚠️ 不受信任的外部脚本:');
          untrustedScripts.forEach(script => {
            console.log(`   - ${script.substring(0, 100)}...`);
          });
        } else {
          console.log('✅ 所有外部脚本来自受信任域名');
        }
        
        // 检查内联脚本
        const inlineScripts = html.match(/<script[^>]*>[\s\S]*?<\/script>/gi) || [];
        const suspiciousInlineScripts = inlineScripts.filter(script => {
          return script.includes('eval(') || 
                 script.includes('document.write(') ||
                 script.includes('innerHTML') ||
                 script.includes('outerHTML');
        });
        
        if (suspiciousInlineScripts.length > 0) {
          risks.push(`发现${suspiciousInlineScripts.length}个可疑内联脚本`);
          console.log('⚠️ 发现可疑内联脚本');
        } else {
          console.log('✅ 内联脚本安全');
        }
        
        resolve(risks);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ CDN检查失败: ${err.message}`);
      resolve(['CDN检查失败']);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ CDN检查超时');
      resolve(['CDN检查超时']);
    });
  });
}

// 4. 检查HTTPS风险
function checkHTTPSRisk(siteUrl) {
  console.log('\n🔐 检查HTTPS风险:');
  
  const risks = [];
  
  if (!siteUrl.startsWith('https://')) {
    if (siteUrl.includes('localhost') || siteUrl.includes('127.0.0.1')) {
      console.log('✅ 本地开发环境，HTTP可接受');
    } else {
      risks.push('生产环境未使用HTTPS');
      console.log('❌ 生产环境未使用HTTPS');
    }
  } else {
    console.log('✅ 使用HTTPS');
  }
  
  // 检查是否有混合内容
  const productionUrl = process.env.NEXT_PUBLIC_WEB_URL;
  if (productionUrl && productionUrl.startsWith('https://')) {
    console.log('✅ 生产环境配置为HTTPS');
  } else if (productionUrl) {
    risks.push('生产环境URL配置为HTTP');
    console.log('❌ 生产环境URL配置为HTTP');
  }
  
  return risks;
}

// 5. 检查CSP违规风险
function checkCSPRisk() {
  console.log('\n🛡️ 检查CSP违规风险:');
  
  const risks = [];
  const configPath = path.join(__dirname, '../next.config.mjs');
  
  if (!fs.existsSync(configPath)) {
    risks.push('缺少CSP配置');
    console.log('❌ 缺少next.config.mjs文件');
    return risks;
  }
  
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  // 检查是否允许unsafe-inline
  if (configContent.includes("'unsafe-inline'")) {
    console.log('⚠️ CSP允许unsafe-inline（可能有风险）');
    // 检查是否有nonce或hash
    if (configContent.includes("'nonce-") || configContent.includes("'sha256-")) {
      console.log('✅ 使用nonce或hash，相对安全');
    } else {
      risks.push('CSP允许unsafe-inline且未使用nonce/hash');
      console.log('❌ 未使用nonce或hash保护');
    }
  } else {
    console.log('✅ CSP不允许unsafe-inline');
  }
  
  // 检查是否允许unsafe-eval
  if (configContent.includes("'unsafe-eval'")) {
    risks.push('CSP允许unsafe-eval（高风险）');
    console.log('❌ CSP允许unsafe-eval');
  } else {
    console.log('✅ CSP不允许unsafe-eval');
  }
  
  return risks;
}

// 6. 检查隐私政策和用户协议
function checkLegalPages(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n📄 检查隐私政策和用户协议:');
    
    const risks = [];
    const pages = [
      { name: '隐私政策', path: '/privacy-policy' },
      { name: '用户协议', path: '/terms-of-service' }
    ];
    
    const checkPage = (page) => {
      return new Promise((resolvePage) => {
        const url = `${siteUrl}${page.path}`;
        const protocol = url.startsWith('https') ? https : http;
        
        const req = protocol.get(url, (res) => {
          console.log(`📍 ${page.name}: ${res.statusCode} ${res.statusCode === 200 ? '✅' : '❌'}`);
          
          if (res.statusCode === 200) {
            let content = '';
            res.on('data', chunk => content += chunk);
            res.on('end', () => {
              // 检查内容是否充实
              if (content.length < 1000) {
                console.log(`   ⚠️ ${page.name}内容过短`);
                resolvePage(`${page.name}内容不充实`);
              } else {
                console.log(`   ✅ ${page.name}内容充实`);
                resolvePage(null);
              }
            });
          } else {
            resolvePage(`${page.name}页面不可访问`);
          }
        });
        
        req.on('error', () => {
          resolvePage(`${page.name}页面检查失败`);
        });
        
        req.setTimeout(5000, () => {
          req.destroy();
          resolvePage(`${page.name}页面检查超时`);
        });
      });
    };
    
    Promise.all(pages.map(checkPage)).then(results => {
      const pageRisks = results.filter(Boolean);
      resolve(pageRisks);
    });
  });
}

// 7. 生成安全审计报告
function generateSecurityReport(phishingRisks, oneTapRisks, cdnRisks, httpsRisks, cspRisks, legalRisks) {
  console.log('\n📋 Google安全风险审计报告:');
  console.log('=' * 60);
  
  const allRisks = [
    ...phishingRisks,
    ...oneTapRisks,
    ...cdnRisks,
    ...httpsRisks,
    ...cspRisks,
    ...legalRisks
  ];
  
  let score = 100;
  const highRisks = [];
  const mediumRisks = [];
  const lowRisks = [];
  
  // 分类风险
  allRisks.forEach(risk => {
    if (risk.includes('钓鱼') || risk.includes('One Tap') || risk.includes('HTTPS') || risk.includes('unsafe-eval')) {
      highRisks.push(risk);
      score -= 25;
    } else if (risk.includes('CSP') || risk.includes('内联脚本') || risk.includes('外部脚本')) {
      mediumRisks.push(risk);
      score -= 15;
    } else {
      lowRisks.push(risk);
      score -= 10;
    }
  });
  
  score = Math.max(0, score);
  
  console.log(`\n📊 Google安全评分: ${score}/100`);
  
  if (score >= 95) {
    console.log('🏆 网站安全性优秀，符合Google标准');
  } else if (score >= 80) {
    console.log('✅ 网站安全性良好，基本符合Google标准');
  } else if (score >= 60) {
    console.log('⚠️ 网站存在安全风险，需要改进');
  } else {
    console.log('❌ 网站存在严重安全风险，可能被Google标记');
  }
  
  if (highRisks.length > 0) {
    console.log('\n🚨 高风险问题:');
    highRisks.forEach(risk => console.log(`   - ${risk}`));
  }
  
  if (mediumRisks.length > 0) {
    console.log('\n⚠️ 中等风险问题:');
    mediumRisks.forEach(risk => console.log(`   - ${risk}`));
  }
  
  if (lowRisks.length > 0) {
    console.log('\n💡 低风险问题:');
    lowRisks.forEach(risk => console.log(`   - ${risk}`));
  }
  
  console.log('\n🔧 修复建议:');
  if (highRisks.length > 0) {
    console.log('1. 立即修复高风险问题');
    console.log('2. 确保使用官方OAuth流程');
    console.log('3. 启用HTTPS并配置正确的CSP');
  }
  if (mediumRisks.length > 0) {
    console.log('4. 优化CSP配置，减少unsafe规则');
    console.log('5. 审查所有外部脚本来源');
  }
  if (lowRisks.length > 0) {
    console.log('6. 完善法律页面内容');
    console.log('7. 添加必要的用户提示');
  }
  
  return score;
}

// 主函数
async function main() {
  try {
    const phishingRisks = await checkPhishingRisk(SITE_URL);
    const oneTapRisks = checkOneTapRisk();
    const cdnRisks = await checkCDNRisk(SITE_URL);
    const httpsRisks = checkHTTPSRisk(SITE_URL);
    const cspRisks = checkCSPRisk();
    const legalRisks = await checkLegalPages(SITE_URL);
    
    const score = generateSecurityReport(phishingRisks, oneTapRisks, cdnRisks, httpsRisks, cspRisks, legalRisks);
    
    console.log('\n🚀 下一步行动:');
    if (score >= 90) {
      console.log('1. 网站安全性优秀，可以放心使用');
      console.log('2. 定期运行安全审计');
    } else {
      console.log('1. 根据上述建议修复安全问题');
      console.log('2. 重新运行安全审计验证修复效果');
      console.log('3. 考虑咨询安全专家');
    }
    
    process.exit(score < 80 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ 安全审计失败:', error.message);
    process.exit(1);
  }
}

main();
