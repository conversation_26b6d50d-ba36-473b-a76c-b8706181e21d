'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  ShoppingCart,
  MessageCircle,
  Mail,
  Star,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign,
  FileText
} from "lucide-react";

interface DashboardStats {
  overview: {
    totalUsers: number;
    totalOrders: number;
    totalFeedbacks: number;
    totalContacts: number;
    totalBills: number;
  };
  feedbacks: {
    total: number;
    averageRating: number;
    byStatus: { [key: string]: number };
    byRating: { [key: number]: number };
  };
  contacts: {
    total: number;
    unread: number;
    read: number;
    replied: number;
    closed: number;
    responseRate: string;
  };
  recentActivity: {
    newUsersToday: number;
    newOrdersToday: number;
    newFeedbacksToday: number;
    newContactsToday: number;
  };
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/dashboard');
      const result = await response.json();

      if (result.code === 0) {
        setStats(result.data);
      } else {
        console.error('Failed to fetch dashboard stats:', result.message);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载统计数据中...</p>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-gray-600">无法加载统计数据</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">管理员仪表板</h1>
          <p className="text-gray-600 dark:text-gray-400">
            系统概览和关键指标
          </p>
        </div>
      </div>

      {/* 概览统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">总用户数</p>
                <p className="text-2xl font-bold">{stats.overview.totalUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
                <ShoppingCart className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">总订单数</p>
                <p className="text-2xl font-bold">{stats.overview.totalOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-lg">
                <MessageCircle className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">用户反馈</p>
                <p className="text-2xl font-bold">{stats.overview.totalFeedbacks}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg">
                <Mail className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">联系我们</p>
                <p className="text-2xl font-bold">{stats.overview.totalContacts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-red-100 dark:bg-red-900/30 p-2 rounded-lg">
                <DollarSign className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">账单记录</p>
                <p className="text-2xl font-bold">{stats.overview.totalBills}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 反馈统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-600" />
              反馈统计
            </CardTitle>
            <CardDescription>用户反馈和评分概览</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">平均评分</span>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="font-bold">{stats.feedbacks.averageRating.toFixed(1)}</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>5星评价</span>
                <span>{stats.feedbacks.byRating[5] || 0}</span>
              </div>
              <Progress
                value={stats.feedbacks.total > 0 ? (stats.feedbacks.byRating[5] || 0) / stats.feedbacks.total * 100 : 0}
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>4星评价</span>
                <span>{stats.feedbacks.byRating[4] || 0}</span>
              </div>
              <Progress
                value={stats.feedbacks.total > 0 ? (stats.feedbacks.byRating[4] || 0) / stats.feedbacks.total * 100 : 0}
                className="h-2"
              />
            </div>

            <div className="grid grid-cols-3 gap-2 pt-2">
              <div className="text-center">
                <p className="text-sm text-gray-600">已创建</p>
                <p className="font-bold text-blue-600">{stats.feedbacks.byStatus.created || 0}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">已处理</p>
                <p className="font-bold text-green-600">{stats.feedbacks.byStatus.processed || 0}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">已关闭</p>
                <p className="font-bold text-gray-600">{stats.feedbacks.byStatus.closed || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 联系我们统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5 text-blue-600" />
              联系我们统计
            </CardTitle>
            <CardDescription>用户联系和回复情况</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">回复率</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {stats.contacts.responseRate}%
              </Badge>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">未读</span>
                </div>
                <span className="font-bold text-red-600">{stats.contacts.unread}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">已读</span>
                </div>
                <span className="font-bold text-blue-600">{stats.contacts.read}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">已回复</span>
                </div>
                <span className="font-bold text-green-600">{stats.contacts.replied}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                  <span className="text-sm">已关闭</span>
                </div>
                <span className="font-bold text-gray-600">{stats.contacts.closed}</span>
              </div>
            </div>

            <div className="pt-2">
              <Progress
                value={parseFloat(stats.contacts.responseRate)}
                className="h-2"
              />
              <p className="text-xs text-gray-500 mt-1">
                总计 {stats.contacts.total} 条联系记录
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-purple-600" />
            快速操作
          </CardTitle>
          <CardDescription>常用管理功能快速入口</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a
              href="/admin/users"
              className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <Users className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium">用户管理</p>
                <p className="text-sm text-gray-600">管理系统用户</p>
              </div>
            </a>

            <a
              href="/admin/orders"
              className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <ShoppingCart className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium">订单管理</p>
                <p className="text-sm text-gray-600">查看订单信息</p>
              </div>
            </a>

            <a
              href="/admin/feedbacks"
              className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <MessageCircle className="w-5 h-5 text-purple-600" />
              <div>
                <p className="font-medium">反馈管理</p>
                <p className="text-sm text-gray-600">处理用户反馈</p>
              </div>
            </a>

            <a
              href="/admin/contacts"
              className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <Mail className="w-5 h-5 text-orange-600" />
              <div>
                <p className="font-medium">联系我们</p>
                <p className="text-sm text-gray-600">处理用户咨询</p>
              </div>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
