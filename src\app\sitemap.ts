import { MetadataRoute } from 'next'
import { locales } from '@/i18n/locale'
import { getPostsByLocale } from '@/models/post'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top'
  
  // 基础页面
  const staticPages = [
    '',
    '/pricing',
    '/posts',
  ]

  // 法律页面
  const legalPages = [
    '/privacy-policy',
    '/terms-of-service',
  ]

  // 功能页面
  const featurePages = [
    '/remove-watermark',
    '/contact',
    '/showcase',
  ]
  
  const sitemap: MetadataRoute.Sitemap = []
  
  // 为每种语言生成页面
  for (const locale of locales) {
    const localePrefix = locale === 'en' ? '' : `/${locale}`
    
    // 静态页面
    for (const page of staticPages) {
      sitemap.push({
        url: `${baseUrl}${localePrefix}${page}`,
        lastModified: new Date(),
        changeFrequency: page === '' ? 'daily' : 'weekly',
        priority: page === '' ? 1.0 : page === '/pricing' ? 0.9 : 0.8,
      })
    }
    
    // 功能页面（高优先级）
    for (const page of featurePages) {
      sitemap.push({
        url: `${baseUrl}${localePrefix}${page}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: page === '/remove-watermark' ? 0.9 : 0.7,
      })
    }

    // 法律页面（较低优先级）
    for (const page of legalPages) {
      sitemap.push({
        url: `${baseUrl}${localePrefix}${page}`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      })
    }
    
    // 博客文章
    try {
      const posts = await getPostsByLocale(locale)
      if (posts) {
        for (const post of posts) {
          if (post.slug && post.status === 'published') {
            sitemap.push({
              url: `${baseUrl}${localePrefix}/posts/${post.slug}`,
              lastModified: post.updated_at ? new Date(post.updated_at) : new Date(post.created_at || ''),
              changeFrequency: 'weekly',
              priority: 0.6,
            })
          }
        }
      }
    } catch (error) {
      console.error('Error fetching posts for sitemap:', error)
    }
  }
  
  return sitemap
}
