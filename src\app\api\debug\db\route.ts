import { respData, respErr } from "@/lib/resp";
import { db } from "@/db";
import { posts } from "@/db/schema";
import { isCurrentUserAdmin } from "@/services/admin";

export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 直接查询数据库
    const allPosts = await db().select().from(posts);

    return respData({
      totalPosts: allPosts.length,
      posts: allPosts.map(p => ({
        uuid: p.uuid,
        title: p.title,
        slug: p.slug,
        locale: p.locale,
        status: p.status,
        created_at: p.created_at
      })),
      message: "直接数据库查询结果"
    });
  } catch (e) {
    console.log("debug db failed: ", e);
    return respErr("数据库查询失败: " + e.message);
  }
}
