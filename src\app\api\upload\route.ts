import { respData, respErr } from "@/lib/resp";
import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";
import { getUserUuid } from "@/services/user";

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

export async function POST(req: Request) {
  try {
    // 检查用户权限
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("请先登录", 401);
    }

    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return respErr("请选择文件");
    }

    // 验证文件类型
    if (!ALLOWED_TYPES.includes(file.type)) {
      return respErr("不支持的文件类型，请上传 JPG、PNG、GIF 或 WebP 格式的图片");
    }

    // 验证文件大小
    if (file.size > MAX_FILE_SIZE) {
      return respErr("文件大小不能超过 10MB");
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${getUuid()}.${fileExtension}`;
    const key = `blog/${fileName}`;

    // 转换文件为Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // 上传到存储服务
    const storage = newStorage();
    const uploadResult = await storage.uploadFile({
      body: buffer,
      key,
      contentType: file.type,
      disposition: "inline",
    });

    return respData({
      url: uploadResult.url,
      filename: fileName,
      originalName: file.name,
      size: file.size,
      type: file.type,
    });
  } catch (e) {
    console.log("upload file failed: ", e);
    return respErr("文件上传失败");
  }
}
