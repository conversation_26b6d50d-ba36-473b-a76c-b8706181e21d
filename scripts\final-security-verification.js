#!/usr/bin/env node

/**
 * 最终安全验证脚本
 * 验证所有Google安全要求是否满足
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env' });

const https = require('https');
const http = require('http');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🔐 最终安全验证...');
console.log(`验证网站: ${SITE_URL}`);

// 验证安全头部
function verifySecurityHeaders(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🛡️ 验证安全头部:');
    
    const protocol = siteUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(siteUrl, (res) => {
      const headers = res.headers;
      const securityScore = {};
      
      // 检查CSP
      const csp = headers['content-security-policy'];
      if (csp) {
        console.log('✅ Content-Security-Policy: 已设置');
        securityScore.csp = !csp.includes('unsafe-eval') ? 25 : 10;
        if (csp.includes('unsafe-eval')) {
          console.log('   ❌ 包含unsafe-eval');
        } else {
          console.log('   ✅ 不包含unsafe-eval');
        }
      } else {
        console.log('❌ Content-Security-Policy: 未设置');
        securityScore.csp = 0;
      }
      
      // 检查X-Frame-Options
      if (headers['x-frame-options']) {
        console.log('✅ X-Frame-Options: 已设置');
        securityScore.frameOptions = 15;
      } else {
        console.log('❌ X-Frame-Options: 未设置');
        securityScore.frameOptions = 0;
      }
      
      // 检查X-XSS-Protection
      if (headers['x-xss-protection']) {
        console.log('✅ X-XSS-Protection: 已设置');
        securityScore.xssProtection = 15;
      } else {
        console.log('❌ X-XSS-Protection: 未设置');
        securityScore.xssProtection = 0;
      }
      
      // 检查X-Content-Type-Options
      if (headers['x-content-type-options']) {
        console.log('✅ X-Content-Type-Options: 已设置');
        securityScore.contentTypeOptions = 10;
      } else {
        console.log('❌ X-Content-Type-Options: 未设置');
        securityScore.contentTypeOptions = 0;
      }
      
      // 检查Referrer-Policy
      if (headers['referrer-policy']) {
        console.log('✅ Referrer-Policy: 已设置');
        securityScore.referrerPolicy = 10;
      } else {
        console.log('❌ Referrer-Policy: 未设置');
        securityScore.referrerPolicy = 0;
      }
      
      const totalScore = Object.values(securityScore).reduce((a, b) => a + b, 0);
      console.log(`\n📊 安全头部评分: ${totalScore}/75`);
      
      resolve(totalScore);
    });
    
    req.on('error', (err) => {
      console.log(`❌ 安全头部检查失败: ${err.message}`);
      resolve(0);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 安全头部检查超时');
      resolve(0);
    });
  });
}

// 验证HTTPS配置
function verifyHTTPS(siteUrl) {
  console.log('\n🔐 验证HTTPS配置:');
  
  if (siteUrl.startsWith('https://')) {
    console.log('✅ 使用HTTPS协议');
    return 25;
  } else {
    console.log('❌ 未使用HTTPS协议');
    return 0;
  }
}

// 验证登录安全
function verifyLoginSecurity(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🔐 验证登录安全:');
    
    const url = `${siteUrl}/api/auth/providers`;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const providers = JSON.parse(data);
          
          if (providers.google && providers.google.type === 'oidc') {
            console.log('✅ Google OAuth正确配置');
            console.log('✅ 使用官方OIDC协议');
            resolve(25);
          } else {
            console.log('❌ Google OAuth配置异常');
            resolve(0);
          }
        } catch (e) {
          console.log('❌ 登录配置检查失败');
          resolve(0);
        }
      });
    });
    
    req.on('error', () => {
      console.log('❌ 登录安全检查失败');
      resolve(0);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ 登录安全检查超时');
      resolve(0);
    });
  });
}

// 生成最终安全报告
function generateFinalReport(headersScore, httpsScore, loginScore) {
  console.log('\n📋 最终安全验证报告:');
  console.log('=' * 50);
  
  const totalScore = headersScore + httpsScore + loginScore;
  const maxScore = 125;
  const percentage = Math.round((totalScore / maxScore) * 100);
  
  console.log(`📊 安全头部: ${headersScore}/75`);
  console.log(`🔐 HTTPS配置: ${httpsScore}/25`);
  console.log(`🔑 登录安全: ${loginScore}/25`);
  console.log(`\n🎯 总评分: ${totalScore}/${maxScore} (${percentage}%)`);
  
  if (percentage >= 90) {
    console.log('🏆 安全等级: 优秀');
    console.log('✅ 完全符合Google安全标准');
  } else if (percentage >= 75) {
    console.log('✅ 安全等级: 良好');
    console.log('✅ 基本符合Google安全标准');
  } else if (percentage >= 60) {
    console.log('⚠️ 安全等级: 一般');
    console.log('⚠️ 需要进一步改进');
  } else {
    console.log('❌ 安全等级: 不足');
    console.log('❌ 存在严重安全风险');
  }
  
  console.log('\n🔍 Google风险评估:');
  
  const riskFactors = [];
  
  if (headersScore < 50) {
    riskFactors.push('安全头部配置不足');
  }
  if (httpsScore === 0) {
    riskFactors.push('未使用HTTPS');
  }
  if (loginScore === 0) {
    riskFactors.push('登录安全配置异常');
  }
  
  if (riskFactors.length === 0) {
    console.log('✅ 无重大安全风险');
    console.log('✅ 不太可能被Google标记为危险网站');
  } else {
    console.log('⚠️ 发现安全风险:');
    riskFactors.forEach(risk => console.log(`   - ${risk}`));
  }
  
  console.log('\n🚀 部署建议:');
  if (percentage >= 75) {
    console.log('✅ 可以安全部署到生产环境');
    console.log('✅ 符合企业级安全标准');
    console.log('✅ 通过Google安全检查');
  } else {
    console.log('⚠️ 建议修复安全问题后再部署');
    console.log('⚠️ 可能影响用户信任度');
  }
  
  return percentage;
}

// 主函数
async function main() {
  try {
    const headersScore = await verifySecurityHeaders(SITE_URL);
    const httpsScore = verifyHTTPS(SITE_URL);
    const loginScore = await verifyLoginSecurity(SITE_URL);
    
    const finalScore = generateFinalReport(headersScore, httpsScore, loginScore);
    
    console.log('\n🎉 验证完成!');
    console.log(`最终安全评分: ${finalScore}%`);
    
    process.exit(finalScore >= 75 ? 0 : 1);
    
  } catch (error) {
    console.error('❌ 安全验证失败:', error.message);
    process.exit(1);
  }
}

main();
