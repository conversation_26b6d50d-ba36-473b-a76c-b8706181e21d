/**
 * 生成URL友好的slug
 * @param title 标题
 * @returns URL友好的slug
 */
export function generateSlug(title: string): string {
  if (!title) return '';

  return title
    .toLowerCase()
    // 移除特殊字符，保留字母、数字、空格、连字符
    .replace(/[^\w\s\u4e00-\u9fff-]/g, '')
    // 将中文字符转换为拼音（简单处理）
    .replace(/[\u4e00-\u9fff]/g, (char) => {
      // 这里可以集成更完善的中文转拼音库
      // 目前简单处理：移除中文字符或用连字符替代
      return '-';
    })
    // 将空格和多个连字符替换为单个连字符
    .replace(/[\s_]+/g, '-')
    // 移除开头和结尾的连字符
    .replace(/^-+|-+$/g, '')
    // 移除连续的连字符
    .replace(/-+/g, '-')
    // 限制长度
    .substring(0, 100);
}

/**
 * 验证slug是否有效
 * @param slug 要验证的slug
 * @returns 是否有效
 */
export function isValidSlug(slug: string): boolean {
  if (!slug) return false;
  
  // slug应该只包含小写字母、数字和连字符
  const slugRegex = /^[a-z0-9-]+$/;
  return slugRegex.test(slug) && 
         slug.length > 0 && 
         slug.length <= 100 &&
         !slug.startsWith('-') && 
         !slug.endsWith('-') &&
         !slug.includes('--');
}

/**
 * 清理和标准化slug
 * @param slug 原始slug
 * @returns 清理后的slug
 */
export function sanitizeSlug(slug: string): string {
  if (!slug) return '';
  
  return slug
    .toLowerCase()
    .replace(/[^\w-]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 100);
}

/**
 * 为中文标题生成更好的slug
 * @param title 中文标题
 * @returns 英文slug
 */
export function generateSlugForChinese(title: string): string {
  if (!title) return '';

  // 简单的中文关键词到英文的映射
  const chineseToEnglish: { [key: string]: string } = {
    '如何': 'how-to',
    '什么是': 'what-is',
    '为什么': 'why',
    '教程': 'tutorial',
    '指南': 'guide',
    '技巧': 'tips',
    '方法': 'method',
    '工具': 'tool',
    '软件': 'software',
    '应用': 'app',
    '网站': 'website',
    '博客': 'blog',
    '文章': 'article',
    '新闻': 'news',
    '更新': 'update',
    '发布': 'release',
    '介绍': 'introduction',
    '评测': 'review',
    '比较': 'comparison',
    '最佳': 'best',
    '推荐': 'recommended',
    '免费': 'free',
    '开源': 'open-source',
  };

  let slug = title.toLowerCase();
  
  // 替换常见中文词汇
  Object.entries(chineseToEnglish).forEach(([chinese, english]) => {
    slug = slug.replace(new RegExp(chinese, 'g'), english);
  });

  // 移除剩余的中文字符
  slug = slug.replace(/[\u4e00-\u9fff]/g, '');
  
  // 标准化处理
  return sanitizeSlug(slug) || 'post';
}
