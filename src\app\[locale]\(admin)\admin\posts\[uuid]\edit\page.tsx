import {
  PostStatus,
  findPostBySlug,
  findPostByUuid,
  updatePost,
} from "@/models/post";

import Empty from "@/components/blocks/empty";
import Header from "@/components/dashboard/header";
import { getUserInfo } from "@/services/user";
import PostFormWrapper from "./post-form-wrapper";

export default async function ({
  params,
}: {
  params: Promise<{ uuid: string }>;
}) {
  const { uuid } = await params;
  const user = await getUserInfo();
  if (!user || !user.uuid) {
    return <Empty message="no auth" />;
  }

  const post = await findPostByUuid(uuid);
  if (!post) {
    return <Empty message="post not found" />;
  }

  const handleSubmit = async (formData: any) => {
    "use server";

    const { title, slug, locale, status, description, cover_url, author_name, author_avatar_url, content } = formData;

    if (!title?.trim() || !slug?.trim() || !locale?.trim()) {
      throw new Error("请填写必填字段");
    }

    const existPost = await findPostBySlug(slug, locale);
    if (existPost && existPost.uuid !== post.uuid) {
      throw new Error("相同链接的文章已存在");
    }

    const updatedPost = {
      updated_at: new Date(),
      status,
      title,
      slug,
      locale,
      description,
      cover_url,
      author_name,
      author_avatar_url,
      content,
    };

    try {
      await updatePost(post.uuid, updatedPost);
      return {
        status: "success",
        message: "文章更新成功",
      };
    } catch (err: any) {
      throw new Error(err.message);
    }
  };

  return (
    <>
      <Header crumb={{
        items: [
          { title: "文章管理", url: "/admin/posts" },
          { title: "编辑文章", is_active: true },
        ]
      }} />

      <div className="w-full px-4 md:px-8 py-8">
        <h1 className="text-2xl font-medium mb-8">编辑文章</h1>
        <PostFormWrapper
          initialData={post}
          onSubmit={handleSubmit}
        />
      </div>
    </>
  );
}
