#!/usr/bin/env node

/**
 * 生产环境变量检查脚本
 * 检查部署后需要修改的环境变量
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env' });

const https = require('https');
const http = require('http');

console.log('🔍 生产环境变量检查...');

// 检查当前环境变量
function checkEnvironmentVariables() {
  console.log('\n📋 环境变量检查:');
  
  const requiredVars = {
    // 认证相关
    'AUTH_SECRET': process.env.AUTH_SECRET,
    'AUTH_URL': process.env.AUTH_URL,
    'NEXTAUTH_URL': process.env.NEXTAUTH_URL,
    'AUTH_GOOGLE_ID': process.env.AUTH_GOOGLE_ID,
    'AUTH_GOOGLE_SECRET': process.env.AUTH_GOOGLE_SECRET,
    
    // 网站配置
    'NEXT_PUBLIC_WEB_URL': process.env.NEXT_PUBLIC_WEB_URL,
    
    // 支付配置
    'PAYPAL_CLIENT_ID': process.env.PAYPAL_CLIENT_ID,
    'PAYPAL_CLIENT_SECRET': process.env.PAYPAL_CLIENT_SECRET,
    'PAYPAL_ENVIRONMENT': process.env.PAYPAL_ENVIRONMENT,
    
    // 数据库
    'DATABASE_URL': process.env.DATABASE_URL,
    
    // API配置
    'WATERMARK_API_URL': process.env.WATERMARK_API_URL,
    'WATERMARK_APP_ID': process.env.WATERMARK_APP_ID,
    'WATERMARK_SECRET_CODE': process.env.WATERMARK_SECRET_CODE,
  };
  
  const issues = [];
  const warnings = [];
  
  Object.entries(requiredVars).forEach(([key, value]) => {
    if (!value) {
      issues.push(`${key} 未设置`);
      console.log(`❌ ${key}: 未设置`);
    } else {
      console.log(`✅ ${key}: 已设置`);
      
      // 检查生产环境特定配置
      if (key === 'NEXT_PUBLIC_WEB_URL' && value.includes('localhost')) {
        warnings.push('NEXT_PUBLIC_WEB_URL 仍指向localhost');
      }
      
      if (key === 'AUTH_URL' && value.includes('localhost')) {
        warnings.push('AUTH_URL 仍指向localhost');
      }
      
      if (key === 'NEXTAUTH_URL' && value.includes('localhost')) {
        warnings.push('NEXTAUTH_URL 仍指向localhost');
      }
      
      if (key === 'PAYPAL_ENVIRONMENT' && value === 'sandbox') {
        warnings.push('PayPal 仍在沙盒环境');
      }
    }
  });
  
  return { issues, warnings };
}

// 检查生产环境URL配置
function checkProductionURLs() {
  console.log('\n🌐 生产环境URL检查:');
  
  const webUrl = process.env.NEXT_PUBLIC_WEB_URL;
  const authUrl = process.env.AUTH_URL;
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  
  const urlIssues = [];
  
  if (webUrl) {
    if (webUrl.startsWith('https://')) {
      console.log(`✅ NEXT_PUBLIC_WEB_URL: ${webUrl} (HTTPS)`);
    } else {
      urlIssues.push('NEXT_PUBLIC_WEB_URL 未使用HTTPS');
      console.log(`❌ NEXT_PUBLIC_WEB_URL: ${webUrl} (非HTTPS)`);
    }
  }
  
  if (authUrl) {
    if (authUrl.startsWith('https://')) {
      console.log(`✅ AUTH_URL: ${authUrl} (HTTPS)`);
    } else {
      urlIssues.push('AUTH_URL 未使用HTTPS');
      console.log(`❌ AUTH_URL: ${authUrl} (非HTTPS)`);
    }
  }
  
  if (nextAuthUrl) {
    if (nextAuthUrl.startsWith('https://')) {
      console.log(`✅ NEXTAUTH_URL: ${nextAuthUrl} (HTTPS)`);
    } else {
      urlIssues.push('NEXTAUTH_URL 未使用HTTPS');
      console.log(`❌ NEXTAUTH_URL: ${nextAuthUrl} (非HTTPS)`);
    }
  }
  
  return urlIssues;
}

// 检查支付配置
function checkPaymentConfig() {
  console.log('\n💳 支付配置检查:');
  
  const paymentProvider = process.env.PAYMENT_PROVIDER;
  const paypalEnv = process.env.PAYPAL_ENVIRONMENT;
  const paypalClientId = process.env.PAYPAL_CLIENT_ID;
  const paypalSecret = process.env.PAYPAL_CLIENT_SECRET;
  
  const paymentIssues = [];
  
  console.log(`📊 支付提供商: ${paymentProvider || '未设置'}`);
  
  if (paymentProvider === 'paypal') {
    if (paypalEnv === 'live') {
      console.log('✅ PayPal: 生产环境');
    } else if (paypalEnv === 'sandbox') {
      paymentIssues.push('PayPal 仍在沙盒环境，需要切换到生产环境');
      console.log('⚠️ PayPal: 沙盒环境');
    } else {
      paymentIssues.push('PayPal 环境未设置');
      console.log('❌ PayPal: 环境未设置');
    }
    
    if (paypalClientId && paypalSecret) {
      console.log('✅ PayPal: 凭据已设置');
    } else {
      paymentIssues.push('PayPal 凭据不完整');
      console.log('❌ PayPal: 凭据不完整');
    }
  }
  
  return paymentIssues;
}

// 检查安全配置
function checkSecurityConfig() {
  console.log('\n🔐 安全配置检查:');
  
  const authSecret = process.env.AUTH_SECRET;
  const oneTapEnabled = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED;
  
  const securityIssues = [];
  
  if (authSecret) {
    if (authSecret.length >= 32) {
      console.log('✅ AUTH_SECRET: 长度充足');
    } else {
      securityIssues.push('AUTH_SECRET 长度不足，建议重新生成');
      console.log('⚠️ AUTH_SECRET: 长度不足');
    }
  } else {
    securityIssues.push('AUTH_SECRET 未设置');
    console.log('❌ AUTH_SECRET: 未设置');
  }
  
  if (oneTapEnabled === 'false') {
    console.log('✅ Google One Tap: 已禁用（安全）');
  } else {
    securityIssues.push('Google One Tap 应该禁用以符合安全要求');
    console.log('⚠️ Google One Tap: 未明确禁用');
  }
  
  return securityIssues;
}

// 生成配置建议
function generateConfigSuggestions(envIssues, urlIssues, paymentIssues, securityIssues) {
  console.log('\n📋 生产环境配置报告:');
  console.log('=' * 50);
  
  const allIssues = [
    ...envIssues.issues,
    ...envIssues.warnings,
    ...urlIssues,
    ...paymentIssues,
    ...securityIssues
  ];
  
  if (allIssues.length === 0) {
    console.log('🎉 所有配置都正确！');
    console.log('✅ 可以安全部署到生产环境');
    return true;
  }
  
  console.log(`⚠️ 发现 ${allIssues.length} 个配置问题:`);
  allIssues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
  
  console.log('\n🔧 修复建议:');
  
  if (envIssues.issues.length > 0) {
    console.log('\n📝 缺失的环境变量:');
    envIssues.issues.forEach(issue => {
      console.log(`   - ${issue}`);
    });
  }
  
  if (urlIssues.length > 0) {
    console.log('\n🌐 URL配置修复:');
    console.log('   - 将所有localhost URL改为生产域名');
    console.log('   - 确保使用HTTPS协议');
  }
  
  if (paymentIssues.length > 0) {
    console.log('\n💳 支付配置修复:');
    console.log('   - 切换PayPal到生产环境');
    console.log('   - 使用生产环境的Client ID和Secret');
  }
  
  if (securityIssues.length > 0) {
    console.log('\n🔐 安全配置修复:');
    console.log('   - 生成新的AUTH_SECRET: openssl rand -base64 32');
    console.log('   - 确保Google One Tap已禁用');
  }
  
  console.log('\n📚 详细配置指南:');
  console.log('   查看: docs/PRODUCTION_ENV_SETUP_GUIDE.md');
  
  return false;
}

// 主函数
function main() {
  try {
    const envCheck = checkEnvironmentVariables();
    const urlCheck = checkProductionURLs();
    const paymentCheck = checkPaymentConfig();
    const securityCheck = checkSecurityConfig();
    
    const isReady = generateConfigSuggestions(envCheck, urlCheck, paymentCheck, securityCheck);
    
    console.log('\n🚀 下一步行动:');
    if (isReady) {
      console.log('1. 配置完美，可以部署！');
      console.log('2. 部署后运行功能测试');
    } else {
      console.log('1. 根据上述建议修复配置问题');
      console.log('2. 在Vercel/Netlify中设置正确的环境变量');
      console.log('3. 重新运行此检查脚本');
      console.log('4. 配置Google OAuth生产域名');
      console.log('5. 切换PayPal到生产环境');
    }
    
    process.exit(isReady ? 0 : 1);
    
  } catch (error) {
    console.error('❌ 环境变量检查失败:', error.message);
    process.exit(1);
  }
}

main();
