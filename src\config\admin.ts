// 管理员配置
export const ADMIN_CONFIG = {
  // 管理员邮箱列表 - 在这里添加您的Google账户邮箱
  adminEmails: [
    '<EMAIL>',  // 您的管理员邮箱
    // 示例：'<EMAIL>',
    // 可以添加更多管理员邮箱
  ],
};

// 动态管理员列表（运行时可修改）
let dynamicAdminEmails: string[] = [];

// 检查邮箱是否为管理员
export function isAdminEmail(email: string): boolean {
  return ADMIN_CONFIG.adminEmails.includes(email.toLowerCase());
}

// 从环境变量获取管理员邮箱（可选）
export function getAdminEmailsFromEnv(): string[] {
  const envAdmins = process.env.ADMIN_EMAILS;
  if (envAdmins) {
    return envAdmins.split(',').map(email => email.trim().toLowerCase());
  }
  return [];
}

// 获取所有管理员邮箱（配置文件 + 环境变量 + 动态添加）
export function getAllAdminEmails(): string[] {
  const configEmails = ADMIN_CONFIG.adminEmails.map(email => email.toLowerCase());
  const envEmails = getAdminEmailsFromEnv();
  return [...new Set([...configEmails, ...envEmails, ...dynamicAdminEmails])];
}

// 添加管理员邮箱
export function addAdminEmail(email: string): boolean {
  try {
    const normalizedEmail = email.toLowerCase().trim();
    const allEmails = getAllAdminEmails();

    if (allEmails.includes(normalizedEmail)) {
      return false; // 已存在
    }

    dynamicAdminEmails.push(normalizedEmail);
    return true;
  } catch (error) {
    console.error('Failed to add admin email:', error);
    return false;
  }
}

// 删除管理员邮箱
export function removeAdminEmail(email: string): boolean {
  try {
    const normalizedEmail = email.toLowerCase().trim();

    // 不能删除配置文件中的管理员
    const configEmails = ADMIN_CONFIG.adminEmails.map(e => e.toLowerCase());
    if (configEmails.includes(normalizedEmail)) {
      return false; // 不能删除配置文件中的管理员
    }

    const index = dynamicAdminEmails.indexOf(normalizedEmail);
    if (index > -1) {
      dynamicAdminEmails.splice(index, 1);
      return true;
    }

    return false;
  } catch (error) {
    console.error('Failed to remove admin email:', error);
    return false;
  }
}
