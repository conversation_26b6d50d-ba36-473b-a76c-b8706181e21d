import { respData, respErr } from "@/lib/resp";
import { isCurrentUserAdmin } from "@/services/admin";
import { getUsersTotal } from "@/models/user";
import { getOrdersTotal } from "@/models/order";
import { getFeedbacksTotal, getFeedbacksStats } from "@/models/feedback";
import { getContactsTotal, getContactsStats } from "@/models/contact";
import { getBillsTotal } from "@/models/bill";

export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 并行获取各种统计数据
    const [
      totalUsers,
      totalOrders,
      totalFeedbacks,
      totalContacts,
      totalBills,
      feedbackStats,
      contactStats,
    ] = await Promise.all([
      getUsersTotal(),
      getOrdersTotal(),
      getFeedbacksTotal(),
      getContactsTotal(),
      getBillsTotal(),
      getFeedbacksStats(),
      getContactsStats(),
    ]);

    // 计算一些额外的统计数据
    const dashboardStats = {
      overview: {
        totalUsers: totalUsers || 0,
        totalOrders: totalOrders || 0,
        totalFeedbacks: totalFeedbacks || 0,
        totalContacts: totalContacts || 0,
        totalBills: totalBills || 0,
      },
      feedbacks: {
        total: feedbackStats.total,
        averageRating: feedbackStats.total > 0 
          ? Object.entries(feedbackStats.byRating).reduce(
              (sum, [rating, count]) => sum + parseInt(rating) * count,
              0
            ) / feedbackStats.total
          : 0,
        byStatus: feedbackStats.byStatus,
        byRating: feedbackStats.byRating,
      },
      contacts: {
        total: contactStats.total,
        unread: contactStats.unread,
        read: contactStats.read,
        replied: contactStats.replied,
        closed: contactStats.closed,
        responseRate: contactStats.total > 0 
          ? ((contactStats.replied + contactStats.closed) / contactStats.total * 100).toFixed(1)
          : 0,
      },
      recentActivity: {
        // 这里可以添加最近活动的统计
        newUsersToday: 0, // 需要实现按日期统计的功能
        newOrdersToday: 0,
        newFeedbacksToday: 0,
        newContactsToday: 0,
      }
    };

    return respData(dashboardStats);
  } catch (e) {
    console.log("get dashboard stats failed: ", e);
    return respErr("获取统计数据失败");
  }
}
